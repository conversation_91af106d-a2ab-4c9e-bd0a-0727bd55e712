#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
施工方案编制与审查智能体Web应用
Construction Plan Agent Web Application
"""

from flask import Flask, render_template, request, jsonify, send_file
import json
import os
from construction_plan_agent import ConstructionPlanAgent, ProjectParameters, ProjectType

app = Flask(__name__)
app.config['SECRET_KEY'] = 'construction_plan_agent_2024'

# 初始化智能体
agent = ConstructionPlanAgent()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/analyze_project', methods=['POST'])
def analyze_project():
    """分析工程参数"""
    try:
        data = request.get_json()
        project_description = data.get('description', '')
        
        # 提取工程参数
        params = agent.extract_project_parameters(project_description)
        
        # 生成技术方案
        solution = agent.generate_technical_solution(params)
        
        # 评估风险等级
        risk_level = agent.safety_checker.assess_risk_level(params)
        
        return jsonify({
            'success': True,
            'parameters': {
                'project_type': params.project_type.value,
                'span_length': params.span_length,
                'beam_height': params.beam_height,
                'bridge_width': params.bridge_width,
                'support_height': params.support_height,
                'environmental_factors': params.environmental_factors
            },
            'solution': solution,
            'risk_level': risk_level.value
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/review_plan', methods=['POST'])
def review_plan():
    """审查施工方案"""
    try:
        data = request.get_json()
        plan_content = data.get('content', '')
        project_description = data.get('description', '')
        
        # 提取工程参数
        params = agent.extract_project_parameters(project_description)
        
        # 审查方案
        review_result = agent.review_engine.review_plan(plan_content, params)
        
        # 检查安全措施
        safety_issues = agent.safety_checker.check_safety_measures(plan_content)
        
        return jsonify({
            'success': True,
            'review_result': review_result,
            'safety_issues': safety_issues
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/generate_template', methods=['POST'])
def generate_template():
    """生成方案模板"""
    try:
        data = request.get_json()
        project_description = data.get('description', '')
        
        # 提取工程参数
        params = agent.extract_project_parameters(project_description)
        
        # 生成技术方案
        solution = agent.generate_technical_solution(params)
        
        # 生成方案模板
        template = generate_plan_template(params, solution)
        
        return jsonify({
            'success': True,
            'template': template
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def generate_plan_template(params: ProjectParameters, solution: dict) -> str:
    """生成施工方案模板"""
    template = f"""# {params.project_name}专项施工方案

## 一、编制依据

### 1.1 国家、行业和地方相关规范规程
- GB50204-2015 混凝土结构工程施工质量验收规范
- JGJ162-2008 建筑施工模板安全技术规范
- JGJ166-2008 建筑施工碗扣式钢管脚手架安全技术规范
- JGJ130-2011 建筑施工扣件式钢管脚手架安全技术规范

### 1.2 设计文件
- 施工图纸
- 地质勘查报告

## 二、工程概况

### 2.1 结构概况
- 工程类型：{params.project_type.value}
- 跨径：{params.span_length}m
- 梁高：{params.beam_height}m
- 桥宽：{params.bridge_width}m

### 2.2 现场条件
- 地质条件：{params.geological_conditions}
- 环境因素：{', '.join(params.environmental_factors)}

## 三、模架体系选择

### 3.1 支撑体系
- 类型：{solution['支撑体系']['类型']}
- 立杆间距：{solution['支撑体系']['立杆间距']}
- 水平杆步距：{solution['支撑体系']['水平杆步距']}

### 3.2 模板系统
- 底模：{solution['模板系统']['底模']}
- 侧模：{solution['模板系统']['侧模']}
- 主龙骨：{solution['模板系统']['主龙骨']}
- 次龙骨：{solution['模板系统']['次龙骨']}

## 四、模架设计

### 4.1 基础处理
- 地基处理：{solution['基础处理']['地基处理']}
- 垫层：{solution['基础处理']['垫层']}
- 排水：{solution['基础处理']['排水']}

### 4.2 特殊措施
{chr(10).join(f'- {measure}' for measure in solution['特殊措施'])}

## 五、施工工艺

### 5.1 施工流程
1. 基础处理
2. 搭设排架
3. 排架验收
4. 铺设底模
5. 绑扎钢筋
6. 浇筑混凝土
7. 养生
8. 拆除排架

### 5.2 技术要求
- 排架垂直度：≤L/500且不大于50mm
- 底模高程误差：±10mm
- 模板平整度：5mm

## 六、安全保证措施

### 6.1 高空作业安全
- 作业人员必须系安全带
- 设置防护栏杆和安全网
- 搭设安全通道和作业平台

### 6.2 机械设备安全
- 设备进场前检查验收
- 操作人员持证上岗
- 定期维护保养

### 6.3 用电安全
- 执行"一机一闸一漏一箱"
- 设置漏电保护装置
- 定期检查线路

## 七、应急预案

### 7.1 组织机构
- 应急指挥小组
- 24小时值班制度

### 7.2 应急措施
- 高处坠落应急预案
- 支架坍塌应急预案
- 触电事故应急预案
- 火灾事故应急预案

## 八、计算验证

### 8.1 荷载计算
- 恒载：模板自重 + 混凝土自重
- 活载：施工荷载 + 振捣荷载

### 8.2 结构验算
- 强度验算
- 刚度验算
- 稳定性验算

（详细计算书另附）

---
*本方案由施工方案编制与审查智能体自动生成，请根据具体工程情况进行调整完善。*
"""
    return template

@app.route('/api/get_standards')
def get_standards():
    """获取技术标准数据库"""
    return jsonify(agent.standards_db)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
