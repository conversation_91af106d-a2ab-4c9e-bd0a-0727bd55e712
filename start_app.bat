@echo off
title Construction Plan Agent

echo ========================================
echo    Construction Plan Agent
echo    Starting Web Application...
echo ========================================
echo.

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

echo Python environment: OK
echo.

:: Change to app directory
cd /d "%~dp0"
echo Current directory: %CD%
echo.

:: Check app file
if not exist "complete_app.py" (
    echo Error: complete_app.py not found
    pause
    exit /b 1
)

:: Install dependencies if needed
echo Checking dependencies...
python -c "import flask, pandas, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install flask pandas openpyxl
)

echo Dependencies: OK
echo.

:: Start application
echo Starting Construction Plan Agent...
echo.
echo Web address: http://127.0.0.1:5004
echo Press Ctrl+C to stop
echo ========================================
echo.

:: Start Python app
start "" python complete_app.py

:: Wait for startup
timeout /t 3 /nobreak >nul

:: Open browser
start "" http://127.0.0.1:5004

echo.
echo Browser opened. Service is running...
echo Close this window to stop the service.
pause >nul
