#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入系统
Data Import System for Standards, Materials, and Formulas
"""

import json
import pandas as pd
import sqlite3
import re
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from datetime import datetime

@dataclass
class StandardItem:
    """标准规范数据结构"""
    code: str
    title: str
    category: str
    status: str
    publish_date: str
    implement_date: str
    content: Dict
    keywords: List[str]
    source: str

@dataclass
class MaterialProperty:
    """材料参数数据结构"""
    material_type: str
    grade: str
    properties: Dict
    unit: str
    standard_reference: str
    test_method: str
    notes: str

@dataclass
class CalculationFormula:
    """计算公式数据结构"""
    formula_id: str
    name: str
    category: str
    formula: str
    variables: Dict
    conditions: List[str]
    reference: str
    examples: List[Dict]

class DataImporter:
    """数据导入器"""
    
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.setup_logging()
        self.init_database()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('data_import.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 标准规范表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS standards (
                code TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                category TEXT,
                status TEXT,
                publish_date TEXT,
                implement_date TEXT,
                content TEXT,
                keywords TEXT,
                source TEXT,
                import_date TEXT
            )
        ''')
        
        # 材料参数表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                material_type TEXT NOT NULL,
                grade TEXT NOT NULL,
                properties TEXT NOT NULL,
                unit TEXT,
                standard_reference TEXT,
                test_method TEXT,
                notes TEXT,
                import_date TEXT,
                UNIQUE(material_type, grade)
            )
        ''')
        
        # 计算公式表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS formulas (
                formula_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT,
                formula TEXT NOT NULL,
                variables TEXT,
                conditions TEXT,
                reference TEXT,
                examples TEXT,
                import_date TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def import_standards_from_excel(self, file_path: str) -> int:
        """从Excel文件导入标准规范"""
        try:
            df = pd.read_excel(file_path)
            imported_count = 0
            
            # 检查必需的列
            required_columns = ['标准编号', '标准名称', '类别', '状态']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for _, row in df.iterrows():
                try:
                    standard = StandardItem(
                        code=str(row['标准编号']).strip(),
                        title=str(row['标准名称']).strip(),
                        category=str(row.get('类别', '')).strip(),
                        status=str(row.get('状态', '现行')).strip(),
                        publish_date=str(row.get('发布日期', '')).strip(),
                        implement_date=str(row.get('实施日期', '')).strip(),
                        content=self.parse_standard_content(row),
                        keywords=self.extract_keywords(str(row['标准名称'])),
                        source=file_path
                    )
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO standards 
                        (code, title, category, status, publish_date, implement_date, 
                         content, keywords, source, import_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        standard.code, standard.title, standard.category, standard.status,
                        standard.publish_date, standard.implement_date,
                        json.dumps(standard.content, ensure_ascii=False),
                        json.dumps(standard.keywords, ensure_ascii=False),
                        standard.source, datetime.now().isoformat()
                    ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    self.logger.error(f"导入标准 {row.get('标准编号', 'Unknown')} 失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"成功导入 {imported_count} 个标准规范")
            return imported_count
            
        except Exception as e:
            self.logger.error(f"导入标准规范失败: {e}")
            return 0
    
    def parse_standard_content(self, row: pd.Series) -> Dict:
        """解析标准内容"""
        content = {}
        
        # 适用范围
        if '适用范围' in row and pd.notna(row['适用范围']):
            content['scope'] = str(row['适用范围'])
        
        # 主要内容
        if '主要内容' in row and pd.notna(row['主要内容']):
            content['main_content'] = str(row['主要内容'])
        
        # 技术要求
        if '技术要求' in row and pd.notna(row['技术要求']):
            content['requirements'] = str(row['技术要求']).split('；')
        
        # 检验方法
        if '检验方法' in row and pd.notna(row['检验方法']):
            content['test_methods'] = str(row['检验方法'])
        
        # 相关标准
        if '相关标准' in row and pd.notna(row['相关标准']):
            content['related_standards'] = str(row['相关标准']).split('，')
        
        return content
    
    def import_materials_from_excel(self, file_path: str) -> int:
        """从Excel文件导入材料参数"""
        try:
            df = pd.read_excel(file_path)
            imported_count = 0
            
            # 检查必需的列
            required_columns = ['材料类型', '等级/牌号']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for _, row in df.iterrows():
                try:
                    # 提取材料属性
                    properties = self.extract_material_properties(row)
                    
                    material = MaterialProperty(
                        material_type=str(row['材料类型']).strip(),
                        grade=str(row['等级/牌号']).strip(),
                        properties=properties,
                        unit=str(row.get('单位', '')).strip(),
                        standard_reference=str(row.get('参考标准', '')).strip(),
                        test_method=str(row.get('试验方法', '')).strip(),
                        notes=str(row.get('备注', '')).strip()
                    )
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO materials 
                        (material_type, grade, properties, unit, standard_reference, 
                         test_method, notes, import_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        material.material_type, material.grade,
                        json.dumps(material.properties, ensure_ascii=False),
                        material.unit, material.standard_reference,
                        material.test_method, material.notes,
                        datetime.now().isoformat()
                    ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    self.logger.error(f"导入材料 {row.get('材料类型', 'Unknown')} 失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"成功导入 {imported_count} 个材料参数")
            return imported_count
            
        except Exception as e:
            self.logger.error(f"导入材料参数失败: {e}")
            return 0
    
    def extract_material_properties(self, row: pd.Series) -> Dict:
        """提取材料属性"""
        properties = {}
        
        # 力学性能
        mechanical_props = [
            ('抗压强度', 'fc'), ('抗拉强度', 'ft'), ('弯曲强度', 'fm'),
            ('屈服强度', 'fy'), ('抗拉强度', 'fu'), ('弹性模量', 'E'),
            ('泊松比', 'nu'), ('剪切模量', 'G')
        ]
        
        for chinese_name, english_name in mechanical_props:
            if chinese_name in row and pd.notna(row[chinese_name]):
                try:
                    value = float(str(row[chinese_name]).replace('MPa', '').replace('GPa', '').strip())
                    properties[english_name] = value
                except:
                    properties[english_name] = str(row[chinese_name])
        
        # 物理性能
        physical_props = [
            ('密度', 'density'), ('容重', 'unit_weight'), ('含水率', 'moisture_content'),
            ('导热系数', 'thermal_conductivity'), ('线膨胀系数', 'thermal_expansion')
        ]
        
        for chinese_name, english_name in physical_props:
            if chinese_name in row and pd.notna(row[chinese_name]):
                try:
                    value = float(str(row[chinese_name]).replace('kg/m³', '').replace('kN/m³', '').strip())
                    properties[english_name] = value
                except:
                    properties[english_name] = str(row[chinese_name])
        
        # 其他属性
        other_props = ['耐久性', '适用环境', '施工要求', '质量等级']
        for prop in other_props:
            if prop in row and pd.notna(row[prop]):
                properties[prop] = str(row[prop])
        
        return properties
    
    def import_formulas_from_excel(self, file_path: str) -> int:
        """从Excel文件导入计算公式"""
        try:
            df = pd.read_excel(file_path)
            imported_count = 0
            
            # 检查必需的列
            required_columns = ['公式名称', '公式', '类别']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for _, row in df.iterrows():
                try:
                    formula_id = f"FORMULA_{datetime.now().strftime('%Y%m%d')}_{imported_count:03d}"
                    
                    formula = CalculationFormula(
                        formula_id=formula_id,
                        name=str(row['公式名称']).strip(),
                        category=str(row['类别']).strip(),
                        formula=str(row['公式']).strip(),
                        variables=self.parse_variables(row),
                        conditions=self.parse_conditions(row),
                        reference=str(row.get('参考标准', '')).strip(),
                        examples=self.parse_examples(row)
                    )
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO formulas 
                        (formula_id, name, category, formula, variables, conditions, 
                         reference, examples, import_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        formula.formula_id, formula.name, formula.category, formula.formula,
                        json.dumps(formula.variables, ensure_ascii=False),
                        json.dumps(formula.conditions, ensure_ascii=False),
                        formula.reference,
                        json.dumps(formula.examples, ensure_ascii=False),
                        datetime.now().isoformat()
                    ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    self.logger.error(f"导入公式 {row.get('公式名称', 'Unknown')} 失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"成功导入 {imported_count} 个计算公式")
            return imported_count
            
        except Exception as e:
            self.logger.error(f"导入计算公式失败: {e}")
            return 0
    
    def parse_variables(self, row: pd.Series) -> Dict:
        """解析公式变量"""
        variables = {}
        
        if '变量说明' in row and pd.notna(row['变量说明']):
            var_text = str(row['变量说明'])
            # 解析格式：M-弯矩(kN·m); q-均布荷载(kN/m); L-跨度(m)
            var_pairs = var_text.split(';')
            for pair in var_pairs:
                if '-' in pair:
                    parts = pair.strip().split('-', 1)
                    if len(parts) == 2:
                        var_name = parts[0].strip()
                        var_desc = parts[1].strip()
                        variables[var_name] = var_desc
        
        return variables
    
    def parse_conditions(self, row: pd.Series) -> List[str]:
        """解析适用条件"""
        conditions = []
        
        if '适用条件' in row and pd.notna(row['适用条件']):
            cond_text = str(row['适用条件'])
            conditions = [cond.strip() for cond in cond_text.split('；') if cond.strip()]
        
        return conditions
    
    def parse_examples(self, row: pd.Series) -> List[Dict]:
        """解析计算示例"""
        examples = []
        
        if '计算示例' in row and pd.notna(row['计算示例']):
            example_text = str(row['计算示例'])
            # 简单解析，实际可能需要更复杂的逻辑
            examples.append({
                'description': example_text,
                'input_values': {},
                'result': ''
            })
        
        return examples
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        keywords = []
        
        # 建筑工程关键词库
        keyword_dict = {
            '混凝土': ['混凝土', '砼'],
            '钢筋': ['钢筋', '钢材', '钢结构'],
            '模板': ['模板', '模架', '支撑'],
            '脚手架': ['脚手架', '排架', '支架'],
            '基础': ['基础', '地基', '桩基'],
            '梁': ['梁', '箱梁', '板梁', 'T梁'],
            '柱': ['柱', '墩柱', '立柱'],
            '板': ['板', '楼板', '桥面板'],
            '安全': ['安全', '防护', '保护'],
            '质量': ['质量', '检验', '验收'],
            '施工': ['施工', '建设', '工程'],
            '设计': ['设计', '计算', '验算']
        }
        
        for category, terms in keyword_dict.items():
            if any(term in text for term in terms):
                keywords.append(category)
        
        return keywords
    
    def import_from_json(self, file_path: str, data_type: str) -> int:
        """从JSON文件导入数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if data_type == 'standards':
                return self.import_standards_from_json(data)
            elif data_type == 'materials':
                return self.import_materials_from_json(data)
            elif data_type == 'formulas':
                return self.import_formulas_from_json(data)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
                
        except Exception as e:
            self.logger.error(f"从JSON导入失败: {e}")
            return 0
    
    def import_standards_from_json(self, data: List[Dict]) -> int:
        """从JSON数据导入标准"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        imported_count = 0
        
        for item in data:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO standards 
                    (code, title, category, status, publish_date, implement_date, 
                     content, keywords, source, import_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    item['code'], item['title'], item.get('category', ''),
                    item.get('status', '现行'), item.get('publish_date', ''),
                    item.get('implement_date', ''),
                    json.dumps(item.get('content', {}), ensure_ascii=False),
                    json.dumps(item.get('keywords', []), ensure_ascii=False),
                    'JSON导入', datetime.now().isoformat()
                ))
                imported_count += 1
            except Exception as e:
                self.logger.error(f"导入标准 {item.get('code', 'Unknown')} 失败: {e}")
        
        conn.commit()
        conn.close()
        return imported_count
    
    def export_template_excel(self, template_type: str, output_path: str):
        """导出Excel模板"""
        if template_type == 'standards':
            self.export_standards_template(output_path)
        elif template_type == 'materials':
            self.export_materials_template(output_path)
        elif template_type == 'formulas':
            self.export_formulas_template(output_path)
    
    def export_standards_template(self, output_path: str):
        """导出标准规范模板"""
        template_data = {
            '标准编号': ['GB50204-2015', 'JGJ162-2008'],
            '标准名称': ['混凝土结构工程施工质量验收规范', '建筑施工模板安全技术规范'],
            '类别': ['施工验收', '安全技术'],
            '状态': ['现行', '现行'],
            '发布日期': ['2015-05-01', '2008-06-25'],
            '实施日期': ['2015-12-01', '2008-12-01'],
            '适用范围': ['混凝土结构工程施工质量验收', '建筑施工模板安全技术'],
            '主要内容': ['质量验收标准和方法', '模板安全技术要求'],
            '技术要求': ['模板承载力；支架稳定性', '设计计算；专项方案'],
            '相关标准': ['GB50010,JGJ18', 'GB50204,JGJ130']
        }
        
        df = pd.DataFrame(template_data)
        df.to_excel(output_path, index=False)
        self.logger.info(f"标准规范模板已导出到: {output_path}")
    
    def export_materials_template(self, output_path: str):
        """导出材料参数模板"""
        template_data = {
            '材料类型': ['混凝土', '钢材'],
            '等级/牌号': ['C30', 'Q235'],
            '抗压强度': [20.1, ''],
            '抗拉强度': [1.43, 375],
            '弹性模量': [30000, 206000],
            '密度': [25, 78.5],
            '单位': ['MPa', 'MPa'],
            '参考标准': ['GB50010-2010', 'GB/T700-2006'],
            '试验方法': ['GB/T50081', 'GB/T228'],
            '备注': ['普通混凝土', '碳素结构钢']
        }
        
        df = pd.DataFrame(template_data)
        df.to_excel(output_path, index=False)
        self.logger.info(f"材料参数模板已导出到: {output_path}")
    
    def export_formulas_template(self, output_path: str):
        """导出计算公式模板"""
        template_data = {
            '公式名称': ['简支梁弯矩', '悬臂梁挠度'],
            '类别': ['弯矩计算', '挠度计算'],
            '公式': ['M = q*L²/8', 'f = q*L⁴/(8*E*I)'],
            '变量说明': ['M-弯矩(kN·m);q-均布荷载(kN/m);L-跨度(m)', 'f-挠度(mm);q-荷载(kN/m);L-跨度(m);E-弹性模量(MPa);I-惯性矩(m⁴)'],
            '适用条件': ['简支梁；均布荷载', '悬臂梁；均布荷载'],
            '参考标准': ['GB50017-2017', 'GB50017-2017'],
            '计算示例': ['q=10kN/m,L=6m时,M=45kN·m', 'q=5kN/m,L=3m时,f=计算值']
        }
        
        df = pd.DataFrame(template_data)
        df.to_excel(output_path, index=False)
        self.logger.info(f"计算公式模板已导出到: {output_path}")

# 使用示例
if __name__ == "__main__":
    importer = DataImporter()
    
    # 导出模板
    importer.export_template_excel('standards', '标准规范导入模板.xlsx')
    importer.export_template_excel('materials', '材料参数导入模板.xlsx')
    importer.export_template_excel('formulas', '计算公式导入模板.xlsx')
    
    print("模板文件已生成，请填写数据后使用以下方法导入：")
    print("importer.import_standards_from_excel('标准规范导入模板.xlsx')")
    print("importer.import_materials_from_excel('材料参数导入模板.xlsx')")
    print("importer.import_formulas_from_excel('计算公式导入模板.xlsx')")
