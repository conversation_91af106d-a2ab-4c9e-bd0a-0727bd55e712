#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建施工方案智能体图标
Create Icon for Construction Plan Agent
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """创建应用图标"""
    
    # 创建图标尺寸
    sizes = [16, 32, 48, 64, 128, 256]
    
    for size in sizes:
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 背景渐变色（蓝色到紫色）
        for y in range(size):
            color_ratio = y / size
            r = int(102 + (118 - 102) * color_ratio)  # 102 -> 118
            g = int(126 + (75 - 126) * color_ratio)   # 126 -> 75
            b = int(234 + (162 - 234) * color_ratio)  # 234 -> 162
            
            draw.rectangle([(0, y), (size, y+1)], fill=(r, g, b, 255))
        
        # 绘制圆角
        corner_radius = size // 8
        mask = Image.new('L', (size, size), 0)
        mask_draw = ImageDraw.Draw(mask)
        mask_draw.rounded_rectangle([(0, 0), (size, size)], corner_radius, fill=255)
        
        # 应用圆角蒙版
        img.putalpha(mask)
        
        # 绘制建筑图标
        icon_size = size // 2
        icon_x = (size - icon_size) // 2
        icon_y = (size - icon_size) // 2
        
        # 绘制安全帽
        helmet_size = icon_size // 3
        helmet_x = icon_x + (icon_size - helmet_size) // 2
        helmet_y = icon_y
        
        # 安全帽主体
        draw.ellipse([
            helmet_x, helmet_y,
            helmet_x + helmet_size, helmet_y + helmet_size // 2
        ], fill=(255, 255, 255, 255))
        
        # 安全帽边缘
        draw.rectangle([
            helmet_x - helmet_size // 6, helmet_y + helmet_size // 3,
            helmet_x + helmet_size + helmet_size // 6, helmet_y + helmet_size // 2
        ], fill=(255, 255, 255, 255))
        
        # 绘制建筑结构
        building_width = icon_size * 2 // 3
        building_height = icon_size // 2
        building_x = icon_x + (icon_size - building_width) // 2
        building_y = icon_y + icon_size // 2
        
        # 主体结构
        draw.rectangle([
            building_x, building_y,
            building_x + building_width, building_y + building_height
        ], fill=(255, 255, 255, 200))
        
        # 结构线条
        line_count = 3
        for i in range(1, line_count):
            x = building_x + (building_width * i) // line_count
            draw.line([
                (x, building_y),
                (x, building_y + building_height)
            ], fill=(255, 255, 255, 255), width=max(1, size // 64))
        
        # 横向线条
        for i in range(1, 3):
            y = building_y + (building_height * i) // 3
            draw.line([
                (building_x, y),
                (building_x + building_width, y)
            ], fill=(255, 255, 255, 255), width=max(1, size // 64))
        
        # 保存图标
        img.save(f'icon_{size}x{size}.png', 'PNG')
        print(f"✅ 创建图标: icon_{size}x{size}.png")
    
    # 创建ICO文件（Windows图标）
    try:
        # 加载最大尺寸的图标
        large_icon = Image.open('icon_256x256.png')
        
        # 创建多尺寸ICO文件
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        icons = []
        
        for size in icon_sizes:
            icon = large_icon.resize(size, Image.Resampling.LANCZOS)
            icons.append(icon)
        
        # 保存为ICO文件
        icons[0].save('施工方案智能体.ico', format='ICO', sizes=icon_sizes)
        print("✅ 创建Windows图标: 施工方案智能体.ico")
        
    except Exception as e:
        print(f"⚠️  ICO文件创建失败: {e}")
    
    print("\n🎨 图标创建完成！")

if __name__ == "__main__":
    try:
        create_icon()
    except ImportError:
        print("❌ 缺少PIL库，正在安装...")
        os.system("pip install Pillow")
        try:
            create_icon()
        except Exception as e:
            print(f"❌ 图标创建失败: {e}")
            print("请手动安装Pillow库: pip install Pillow")
