#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试应用 - 简化版
"""

from flask import Flask, render_template_string, request, jsonify

app = Flask(__name__)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工方案智能体 - 测试版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">🏗️ 施工方案智能体 - 测试版</a>
        </div>
    </nav>

    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">系统状态检查</h1>
            <p class="lead mb-4">检查各个功能模块的运行状态</p>
        </div>
    </section>

    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">🔍 基础功能测试</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-primary" onclick="testBasic()">测试基础功能</button>
                            <div id="basicResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">📥 数据导入测试</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-success" onclick="testImport()">测试导入功能</button>
                            <div id="importResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">📊 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div id="systemInfo">
                                <p><strong>服务器状态:</strong> <span class="badge bg-success">运行中</span></p>
                                <p><strong>访问地址:</strong> http://127.0.0.1:5002</p>
                                <p><strong>启动时间:</strong> <span id="startTime"></span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 设置启动时间
        document.getElementById('startTime').textContent = new Date().toLocaleString();

        async function testBasic() {
            const resultDiv = document.getElementById('basicResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 测试中...';
            
            try {
                const response = await fetch('/api/test_basic');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ 基础功能正常</strong><br>
                            Flask版本: ${data.flask_version}<br>
                            Python版本: ${data.python_version}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ 测试失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ 请求失败: ${error.message}</div>`;
            }
        }

        async function testImport() {
            const resultDiv = document.getElementById('importResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 测试中...';
            
            try {
                const response = await fetch('/api/test_import');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ 导入功能正常</strong><br>
                            数据库状态: ${data.db_status}<br>
                            模块状态: ${data.module_status}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ 测试失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ 请求失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/test_basic')
def test_basic():
    """测试基础功能"""
    try:
        import sys
        import flask
        
        return jsonify({
            'success': True,
            'flask_version': flask.__version__,
            'python_version': sys.version.split()[0],
            'message': '基础功能正常'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test_import')
def test_import():
    """测试导入功能"""
    try:
        # 测试导入模块
        from data_importer import DataImporter
        
        # 测试数据库连接
        importer = DataImporter()
        
        return jsonify({
            'success': True,
            'db_status': '连接正常',
            'module_status': '导入正常',
            'message': '导入功能正常'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("启动测试应用...")
    print("访问地址: http://127.0.0.1:5002")
    app.run(debug=True, host='0.0.0.0', port=5002)
