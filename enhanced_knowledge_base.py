#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版知识库系统
Enhanced Knowledge Base System
"""

import json
import sqlite3
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import re
from pathlib import Path

@dataclass
class KnowledgeItem:
    """知识项数据结构"""
    id: str
    type: str  # standard, case, rule, formula
    title: str
    content: Dict
    category: str
    tags: List[str]
    confidence: float
    source: str
    update_date: str
    relationships: List[str] = None

@dataclass
class ProjectCase:
    """工程案例数据结构"""
    case_id: str
    project_name: str
    project_type: str
    parameters: Dict
    solution: Dict
    outcomes: Dict
    lessons_learned: List[str]
    success_factors: List[str]
    risk_factors: List[str]

class EnhancedKnowledgeBase:
    """增强版知识库"""
    
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.init_database()
        self.load_core_knowledge()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建知识表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_items (
                id TEXT PRIMARY KEY,
                type TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                category TEXT,
                tags TEXT,
                confidence REAL,
                source TEXT,
                update_date TEXT,
                relationships TEXT
            )
        ''')
        
        # 创建案例表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS project_cases (
                case_id TEXT PRIMARY KEY,
                project_name TEXT NOT NULL,
                project_type TEXT NOT NULL,
                parameters TEXT NOT NULL,
                solution TEXT NOT NULL,
                outcomes TEXT,
                lessons_learned TEXT,
                success_factors TEXT,
                risk_factors TEXT
            )
        ''')
        
        # 创建规则表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS decision_rules (
                rule_id TEXT PRIMARY KEY,
                condition TEXT NOT NULL,
                action TEXT NOT NULL,
                reference TEXT,
                priority INTEGER,
                category TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def load_core_knowledge(self):
        """加载核心知识"""
        # 加载规范标准
        self.load_standards()
        # 加载材料参数
        self.load_material_properties()
        # 加载计算公式
        self.load_calculation_formulas()
        # 加载决策规则
        self.load_decision_rules()
        # 加载典型案例
        self.load_typical_cases()
    
    def load_standards(self):
        """加载规范标准"""
        standards = [
            {
                "id": "GB50204-2015",
                "type": "standard",
                "title": "混凝土结构工程施工质量验收规范",
                "content": {
                    "scope": "混凝土结构工程施工质量验收",
                    "key_requirements": [
                        "模板及其支架应具有足够的承载能力、刚度和稳定性",
                        "模板的接缝不应漏浆",
                        "模板及其支架拆除的顺序及安全措施应按施工技术方案执行"
                    ],
                    "inspection_points": [
                        "模板安装质量",
                        "支架稳定性",
                        "预埋件位置",
                        "混凝土外观质量"
                    ]
                },
                "category": "施工验收",
                "tags": ["混凝土", "模板", "质量验收"],
                "confidence": 1.0,
                "source": "国家标准",
                "update_date": "2015-12-01"
            },
            {
                "id": "JGJ162-2008",
                "type": "standard",
                "title": "建筑施工模板安全技术规范",
                "content": {
                    "scope": "建筑施工中模板工程的安全技术要求",
                    "key_requirements": [
                        "模板及其支架应进行设计计算",
                        "高大模板支架应编制专项施工方案",
                        "模板支架搭设完毕应进行验收"
                    ],
                    "calculation_requirements": [
                        "承载能力极限状态计算",
                        "正常使用极限状态验算",
                        "稳定性验算"
                    ]
                },
                "category": "模板安全",
                "tags": ["模板", "安全技术", "高大模板"],
                "confidence": 1.0,
                "source": "行业标准",
                "update_date": "2008-10-01"
            }
        ]
        
        for standard in standards:
            self.add_knowledge_item(KnowledgeItem(**standard))
    
    def load_material_properties(self):
        """加载材料参数"""
        materials = {
            "id": "MATERIAL_PROPS",
            "type": "parameters",
            "title": "建筑材料参数库",
            "content": {
                "concrete": {
                    "C30": {"fck": 20.1, "fc": 14.3, "ft": 1.43, "Ec": 30000, "density": 25},
                    "C35": {"fck": 23.4, "fc": 16.7, "ft": 1.57, "Ec": 31500, "density": 25},
                    "C40": {"fck": 26.8, "fc": 19.1, "ft": 1.71, "Ec": 32500, "density": 25}
                },
                "steel": {
                    "Q235": {"fy": 235, "fu": 375, "E": 206000, "density": 78.5},
                    "Q355": {"fy": 355, "fu": 510, "E": 206000, "density": 78.5},
                    "HRB400": {"fy": 400, "fu": 540, "E": 200000, "density": 78.5}
                },
                "wood": {
                    "pine": {"fm": 14.5, "fc": 13.0, "ft": 8.5, "E": 11000, "density": 5.0},
                    "fir": {"fm": 16.0, "fc": 14.0, "ft": 9.0, "E": 12000, "density": 4.5}
                }
            },
            "category": "材料参数",
            "tags": ["混凝土", "钢材", "木材", "力学性能"],
            "confidence": 1.0,
            "source": "国家标准",
            "update_date": "2024-01-01"
        }
        
        self.add_knowledge_item(KnowledgeItem(**materials))
    
    def load_calculation_formulas(self):
        """加载计算公式"""
        formulas = {
            "id": "CALC_FORMULAS",
            "type": "formulas",
            "title": "结构计算公式库",
            "content": {
                "beam_moment": {
                    "simply_supported": "M = q*L²/8",
                    "continuous": "M = q*L²/12",
                    "cantilever": "M = q*L²/2"
                },
                "deflection": {
                    "simply_supported": "f = 5*q*L⁴/(384*E*I)",
                    "cantilever": "f = q*L⁴/(8*E*I)"
                },
                "stability": {
                    "euler": "Ncr = π²*E*I/L²",
                    "column": "N ≤ φ*A*f"
                },
                "concrete_pressure": {
                    "lateral": "F = 0.28*γc*t0*β*V^0.5",
                    "max_pressure": "F = γc*H"
                }
            },
            "category": "计算公式",
            "tags": ["结构计算", "弯矩", "挠度", "稳定性"],
            "confidence": 1.0,
            "source": "结构力学",
            "update_date": "2024-01-01"
        }
        
        self.add_knowledge_item(KnowledgeItem(**formulas))
    
    def load_decision_rules(self):
        """加载决策规则"""
        rules = [
            {
                "rule_id": "RULE_001",
                "condition": "支架高度 > 8m OR 跨度 > 18m OR 荷载 > 15kN/m²",
                "action": "编制专项施工方案并组织专家论证",
                "reference": "建质[2009]87号",
                "priority": 1,
                "category": "高大模板"
            },
            {
                "rule_id": "RULE_002", 
                "condition": "跨越铁路 OR 跨越高速公路",
                "action": "制定交通保护措施，设置防护门洞",
                "reference": "相关交通管理规定",
                "priority": 1,
                "category": "交通保护"
            },
            {
                "rule_id": "RULE_003",
                "condition": "现浇箱梁 AND 跨径 > 30m",
                "action": "推荐使用碗扣式满堂红脚手架",
                "reference": "工程实践经验",
                "priority": 2,
                "category": "技术选择"
            }
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for rule in rules:
            cursor.execute('''
                INSERT OR REPLACE INTO decision_rules 
                (rule_id, condition, action, reference, priority, category)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rule["rule_id"], rule["condition"], rule["action"], 
                  rule["reference"], rule["priority"], rule["category"]))
        
        conn.commit()
        conn.close()
    
    def load_typical_cases(self):
        """加载典型案例"""
        cases = [
            ProjectCase(
                case_id="CASE_001",
                project_name="京台高速4标箱梁工程",
                project_type="现浇箱梁",
                parameters={
                    "spans": [33, 33, 43, 33],
                    "beam_height": 2.0,
                    "bridge_width": 21.0,
                    "support_height": 7.0
                },
                solution={
                    "support_system": "碗扣式满堂红脚手架",
                    "formwork": "清水模板+槽钢龙骨",
                    "foundation": "15cm二灰碎石垫层",
                    "special_measures": ["贝雷片跨河", "门洞保证交通"]
                },
                outcomes={
                    "construction_period": "6个月",
                    "quality_grade": "优良",
                    "safety_record": "零事故",
                    "cost_efficiency": "节约成本8%"
                },
                lessons_learned=[
                    "贝雷片选型要充分考虑承载能力",
                    "门洞设计要满足交通净空要求",
                    "排架基础处理是关键环节"
                ],
                success_factors=[
                    "详细的专项施工方案",
                    "充分的技术交底",
                    "严格的质量控制"
                ],
                risk_factors=[
                    "跨河施工风险",
                    "交通干扰风险",
                    "高大模板风险"
                ]
            )
        ]
        
        for case in cases:
            self.add_project_case(case)
    
    def add_knowledge_item(self, item: KnowledgeItem):
        """添加知识项"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO knowledge_items 
            (id, type, title, content, category, tags, confidence, source, update_date, relationships)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            item.id, item.type, item.title, json.dumps(item.content, ensure_ascii=False),
            item.category, json.dumps(item.tags, ensure_ascii=False), item.confidence,
            item.source, item.update_date, json.dumps(item.relationships, ensure_ascii=False)
        ))
        
        conn.commit()
        conn.close()
    
    def add_project_case(self, case: ProjectCase):
        """添加工程案例"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO project_cases 
            (case_id, project_name, project_type, parameters, solution, outcomes, 
             lessons_learned, success_factors, risk_factors)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            case.case_id, case.project_name, case.project_type,
            json.dumps(case.parameters, ensure_ascii=False),
            json.dumps(case.solution, ensure_ascii=False),
            json.dumps(case.outcomes, ensure_ascii=False),
            json.dumps(case.lessons_learned, ensure_ascii=False),
            json.dumps(case.success_factors, ensure_ascii=False),
            json.dumps(case.risk_factors, ensure_ascii=False)
        ))
        
        conn.commit()
        conn.close()
    
    def search_knowledge(self, query: str, knowledge_type: str = None) -> List[Dict]:
        """搜索知识"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        sql = "SELECT * FROM knowledge_items WHERE title LIKE ? OR content LIKE ?"
        params = [f"%{query}%", f"%{query}%"]
        
        if knowledge_type:
            sql += " AND type = ?"
            params.append(knowledge_type)
        
        cursor.execute(sql, params)
        results = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in results]
    
    def find_similar_cases(self, project_params: Dict) -> List[ProjectCase]:
        """查找相似案例"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM project_cases")
        cases = cursor.fetchall()
        conn.close()
        
        similar_cases = []
        for case_data in cases:
            case_params = json.loads(case_data[3])  # parameters字段
            similarity = self.calculate_similarity(project_params, case_params)
            
            if similarity > 0.6:  # 相似度阈值
                case = ProjectCase(
                    case_id=case_data[0],
                    project_name=case_data[1],
                    project_type=case_data[2],
                    parameters=json.loads(case_data[3]),
                    solution=json.loads(case_data[4]),
                    outcomes=json.loads(case_data[5]) if case_data[5] else {},
                    lessons_learned=json.loads(case_data[6]) if case_data[6] else [],
                    success_factors=json.loads(case_data[7]) if case_data[7] else [],
                    risk_factors=json.loads(case_data[8]) if case_data[8] else []
                )
                similar_cases.append((case, similarity))
        
        # 按相似度排序
        similar_cases.sort(key=lambda x: x[1], reverse=True)
        return [case for case, _ in similar_cases[:5]]
    
    def calculate_similarity(self, params1: Dict, params2: Dict) -> float:
        """计算参数相似度"""
        similarity_score = 0.0
        total_weight = 0.0
        
        # 跨径相似度 (权重: 0.4)
        if 'spans' in params1 and 'spans' in params2:
            max_span1 = max(params1['spans']) if isinstance(params1['spans'], list) else params1['spans']
            max_span2 = max(params2['spans']) if isinstance(params2['spans'], list) else params2['spans']
            span_similarity = 1 - abs(max_span1 - max_span2) / max(max_span1, max_span2)
            similarity_score += span_similarity * 0.4
            total_weight += 0.4
        
        # 梁高相似度 (权重: 0.3)
        if 'beam_height' in params1 and 'beam_height' in params2:
            height_diff = abs(params1['beam_height'] - params2['beam_height'])
            height_similarity = 1 - height_diff / max(params1['beam_height'], params2['beam_height'])
            similarity_score += height_similarity * 0.3
            total_weight += 0.3
        
        # 桥宽相似度 (权重: 0.3)
        if 'bridge_width' in params1 and 'bridge_width' in params2:
            width_diff = abs(params1['bridge_width'] - params2['bridge_width'])
            width_similarity = 1 - width_diff / max(params1['bridge_width'], params2['bridge_width'])
            similarity_score += width_similarity * 0.3
            total_weight += 0.3
        
        return similarity_score / total_weight if total_weight > 0 else 0.0
    
    def get_applicable_rules(self, project_params: Dict) -> List[Dict]:
        """获取适用规则"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM decision_rules ORDER BY priority")
        rules = cursor.fetchall()
        conn.close()
        
        applicable_rules = []
        for rule in rules:
            if self.evaluate_rule_condition(rule[1], project_params):  # condition字段
                applicable_rules.append({
                    'rule_id': rule[0],
                    'condition': rule[1],
                    'action': rule[2],
                    'reference': rule[3],
                    'priority': rule[4],
                    'category': rule[5]
                })
        
        return applicable_rules
    
    def evaluate_rule_condition(self, condition: str, params: Dict) -> bool:
        """评估规则条件"""
        # 简化的条件评估逻辑
        try:
            # 替换条件中的变量
            condition = condition.replace("支架高度", str(params.get('support_height', 0)))
            condition = condition.replace("跨度", str(max(params.get('spans', [0]))))
            condition = condition.replace("荷载", str(params.get('load', 0)))
            
            # 处理逻辑运算符
            condition = condition.replace(" OR ", " or ").replace(" AND ", " and ")
            condition = condition.replace(">", " > ").replace("<", " < ")
            
            # 安全评估（这里需要更复杂的解析逻辑）
            return False  # 暂时返回False，避免执行任意代码
        except:
            return False
    
    def get_material_properties(self, material_type: str, grade: str) -> Dict:
        """获取材料参数"""
        materials = self.search_knowledge("材料参数", "parameters")
        if materials:
            content = json.loads(materials[0]['content'])
            return content.get(material_type, {}).get(grade, {})
        return {}
    
    def get_calculation_formula(self, formula_type: str, case: str) -> str:
        """获取计算公式"""
        formulas = self.search_knowledge("计算公式", "formulas")
        if formulas:
            content = json.loads(formulas[0]['content'])
            return content.get(formula_type, {}).get(case, "")
        return ""

# 使用示例
if __name__ == "__main__":
    # 创建增强版知识库
    kb = EnhancedKnowledgeBase()
    
    # 搜索知识
    results = kb.search_knowledge("模板安全")
    print("搜索结果:", len(results))
    
    # 查找相似案例
    project_params = {
        "spans": [35, 38, 35],
        "beam_height": 2.0,
        "bridge_width": 42.0,
        "support_height": 7.0
    }
    
    similar_cases = kb.find_similar_cases(project_params)
    print("相似案例:", len(similar_cases))
    
    # 获取适用规则
    rules = kb.get_applicable_rules(project_params)
    print("适用规则:", len(rules))
    
    # 获取材料参数
    concrete_props = kb.get_material_properties("concrete", "C30")
    print("C30混凝土参数:", concrete_props)
