@echo off
title Create Desktop Shortcut

echo ========================================
echo   Creating Desktop Shortcut
echo ========================================
echo.

:: Get current directory
set "CURRENT_DIR=%~dp0"
set "TARGET_FILE=%CURRENT_DIR%start_app.bat"

:: Check if target file exists
if not exist "%TARGET_FILE%" (
    echo Error: start_app.bat not found
    echo Please make sure all files are in the same folder
    pause
    exit /b 1
)

echo Current directory: %CURRENT_DIR%
echo Target file: %TARGET_FILE%
echo.

:: Create VBS script to create shortcut
echo Creating shortcut script...

(
echo Set oWS = WScript.CreateObject^("WScript.Shell"^)
echo sLinkFile = oWS.SpecialFolders^("Desktop"^) ^& "\Construction Plan Agent.lnk"
echo Set oLink = oWS.CreateShortcut^(sLinkFile^)
echo oLink.TargetPath = "%TARGET_FILE%"
echo oLink.WorkingDirectory = "%CURRENT_DIR%"
echo oLink.Description = "Construction Plan Agent - One Click Start"
echo oLink.Save
echo WScript.Echo "Desktop shortcut created successfully!"
) > temp_shortcut.vbs

:: Run the VBS script
echo Running shortcut creation script...
cscript //nologo temp_shortcut.vbs

:: Clean up
del temp_shortcut.vbs

echo.
echo ========================================
echo   Shortcut Creation Complete!
echo ========================================
echo.
echo Please check your desktop for:
echo "Construction Plan Agent" shortcut
echo.
echo Double-click the shortcut to start the app!
echo.
pause
