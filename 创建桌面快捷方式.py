#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建桌面快捷方式
Create Desktop Shortcut for Construction Plan Agent
"""

import os
import sys
from pathlib import Path

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent.absolute()
        
        # 获取桌面路径
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "桌面"
        
        if not desktop.exists():
            print("❌ 无法找到桌面目录")
            return False
        
        # 批处理文件路径
        bat_file = current_dir / "启动施工方案智能体.bat"
        
        if not bat_file.exists():
            print("❌ 找不到启动脚本文件")
            return False
        
        # 图标文件路径
        icon_file = current_dir / "施工方案智能体.ico"
        
        # 快捷方式路径
        shortcut_path = desktop / "施工方案智能体.lnk"
        
        # 使用Windows COM创建快捷方式
        try:
            import win32com.client
            
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = str(bat_file)
            shortcut.WorkingDirectory = str(current_dir)
            shortcut.Description = "施工方案编制与审查智能体 - 一键启动Web应用"
            
            if icon_file.exists():
                shortcut.IconLocation = str(icon_file)
            
            shortcut.save()
            
            print(f"✅ 桌面快捷方式创建成功!")
            print(f"📍 位置: {shortcut_path}")
            return True
            
        except ImportError:
            print("⚠️  缺少pywin32库，使用备用方案...")
            return create_shortcut_alternative(bat_file, desktop, icon_file)
            
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def create_shortcut_alternative(bat_file, desktop, icon_file):
    """备用快捷方式创建方案"""
    
    try:
        # 创建VBS脚本来创建快捷方式
        vbs_script = f'''
Set oWS = WScript.CreateObject("WScript.Shell")
sLinkFile = "{desktop}\\施工方案智能体.lnk"
Set oLink = oWS.CreateShortcut(sLinkFile)
oLink.TargetPath = "{bat_file}"
oLink.WorkingDirectory = "{bat_file.parent}"
oLink.Description = "施工方案编制与审查智能体"
'''
        
        if icon_file.exists():
            vbs_script += f'oLink.IconLocation = "{icon_file}"\n'
        
        vbs_script += 'oLink.Save\n'
        
        # 保存VBS脚本
        vbs_file = Path("temp_shortcut.vbs")
        with open(vbs_file, 'w', encoding='utf-8') as f:
            f.write(vbs_script)
        
        # 执行VBS脚本
        os.system(f'cscript //nologo "{vbs_file}"')
        
        # 删除临时文件
        vbs_file.unlink()
        
        print("✅ 桌面快捷方式创建成功!")
        return True
        
    except Exception as e:
        print(f"❌ 备用方案失败: {e}")
        return False

def install_dependencies():
    """安装必要的依赖"""
    
    print("🔍 检查依赖包...")
    
    try:
        import win32com.client
        print("✅ pywin32 已安装")
    except ImportError:
        print("📦 正在安装 pywin32...")
        os.system("pip install pywin32")
    
    try:
        from PIL import Image
        print("✅ Pillow 已安装")
    except ImportError:
        print("📦 正在安装 Pillow...")
        os.system("pip install Pillow")

def main():
    """主函数"""
    
    print("🎨 施工方案智能体 - 桌面快捷方式创建工具")
    print("=" * 50)
    
    # 安装依赖
    install_dependencies()
    
    print("\n🖼️  正在创建图标...")
    
    # 创建图标
    try:
        exec(open('create_icon.py').read())
    except Exception as e:
        print(f"⚠️  图标创建失败: {e}")
    
    print("\n🔗 正在创建桌面快捷方式...")
    
    # 创建快捷方式
    success = create_desktop_shortcut()
    
    if success:
        print("\n🎉 设置完成!")
        print("\n📋 使用说明:")
        print("1. 双击桌面上的'施工方案智能体'图标")
        print("2. 等待应用启动（约3-5秒）")
        print("3. 浏览器将自动打开应用页面")
        print("4. 关闭命令行窗口可停止服务")
        
        print("\n🌐 手动访问地址: http://127.0.0.1:5004")
        
    else:
        print("\n❌ 快捷方式创建失败")
        print("您可以手动创建快捷方式:")
        print(f"目标: {Path(__file__).parent / '启动施工方案智能体.bat'}")
    
    print("\n按任意键退出...")
    input()

if __name__ == "__main__":
    main()
