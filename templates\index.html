<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工方案编制与审查智能体</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .result-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
        }
        .risk-badge {
            font-size: 1.1em;
            padding: 0.5rem 1rem;
        }
        .loading {
            display: none;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-hard-hat me-2"></i>
                施工方案智能体
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#analysis">参数分析</a>
                <a class="nav-link" href="#review">方案审查</a>
                <a class="nav-link" href="#template">模板生成</a>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">施工方案编制与审查智能体</h1>
            <p class="lead mb-4">基于京台4标箱梁模架专项施工方案学习开发的AI助手</p>
            <p class="mb-0">自动分析工程参数 • 智能生成技术方案 • 全面审查安全措施</p>
        </div>
    </section>

    <!-- 功能特性 -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-search fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">智能参数识别</h5>
                            <p class="card-text">自动从工程描述中提取关键参数，包括跨径、梁高、环境因素等</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                            <h5 class="card-title">技术方案生成</h5>
                            <p class="card-text">基于工程特点自动生成合适的支撑体系、模板系统等技术方案</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">安全审查评估</h5>
                            <p class="card-text">全面检查安全措施完整性，评估风险等级，提供改进建议</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 工程参数分析 -->
    <section id="analysis" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-chart-line me-2"></i>
                工程参数分析
            </h2>
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">工程描述输入</h5>
                        </div>
                        <div class="card-body">
                            <textarea id="projectDescription" class="form-control" rows="8" 
                                placeholder="请输入工程描述，例如：&#10;京台高速公路现浇箱梁工程，跨径35+38+35m，梁高2.0m，桥宽42m，上跨现况道路，需设置门洞保证交通。地质条件为粉砂土层。"></textarea>
                            <button id="analyzeBtn" class="btn btn-primary mt-3">
                                <i class="fas fa-play me-2"></i>
                                开始分析
                            </button>
                            <div id="analysisLoading" class="loading mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">分析中...</span>
                                </div>
                                <span class="ms-2">正在分析工程参数...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div id="analysisResult" class="card" style="display: none;">
                        <div class="card-header">
                            <h5 class="mb-0">分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="analysisContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 方案审查 -->
    <section id="review" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-clipboard-check me-2"></i>
                方案审查评估
            </h2>
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">施工方案内容</h5>
                        </div>
                        <div class="card-body">
                            <textarea id="planContent" class="form-control" rows="10" 
                                placeholder="请粘贴施工方案内容进行审查..."></textarea>
                            <div class="mt-3">
                                <input id="planDescription" class="form-control" 
                                    placeholder="请输入工程描述（用于参数提取）">
                            </div>
                            <button id="reviewBtn" class="btn btn-success mt-3">
                                <i class="fas fa-search me-2"></i>
                                开始审查
                            </button>
                            <div id="reviewLoading" class="loading mt-3">
                                <div class="spinner-border text-success" role="status">
                                    <span class="visually-hidden">审查中...</span>
                                </div>
                                <span class="ms-2">正在审查方案...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div id="reviewResult" class="card" style="display: none;">
                        <div class="card-header">
                            <h5 class="mb-0">审查结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="reviewContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 模板生成 -->
    <section id="template" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-file-alt me-2"></i>
                方案模板生成
            </h2>
            <div class="row">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">工程信息</h5>
                        </div>
                        <div class="card-body">
                            <textarea id="templateDescription" class="form-control" rows="6" 
                                placeholder="请输入工程描述..."></textarea>
                            <button id="generateBtn" class="btn btn-info mt-3">
                                <i class="fas fa-magic me-2"></i>
                                生成模板
                            </button>
                            <div id="templateLoading" class="loading mt-3">
                                <div class="spinner-border text-info" role="status">
                                    <span class="visually-hidden">生成中...</span>
                                </div>
                                <span class="ms-2">正在生成模板...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8">
                    <div id="templateResult" class="card" style="display: none;">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">生成的方案模板</h5>
                            <button id="copyTemplate" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-copy me-1"></i>
                                复制
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="templateContent" class="code-block"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">© 2024 施工方案编制与审查智能体 - 基于AI技术的建筑施工辅助系统</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 内联JavaScript以避免静态文件问题
        document.addEventListener('DOMContentLoaded', function() {
            // 工程参数分析
            const analyzeBtn = document.getElementById('analyzeBtn');
            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', analyzeProject);
            }

            // 方案审查
            const reviewBtn = document.getElementById('reviewBtn');
            if (reviewBtn) {
                reviewBtn.addEventListener('click', reviewPlan);
            }

            // 模板生成
            const generateBtn = document.getElementById('generateBtn');
            if (generateBtn) {
                generateBtn.addEventListener('click', generateTemplate);
            }

            // 复制模板
            const copyBtn = document.getElementById('copyTemplate');
            if (copyBtn) {
                copyBtn.addEventListener('click', copyTemplate);
            }
        });

        // 分析工程参数
        async function analyzeProject() {
            const description = document.getElementById('projectDescription').value.trim();

            if (!description) {
                alert('请输入工程描述');
                return;
            }

            const btn = document.getElementById('analyzeBtn');
            const loading = document.getElementById('analysisLoading');
            const result = document.getElementById('analysisResult');

            // 显示加载状态
            btn.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';

            try {
                const response = await fetch('/api/analyze_project', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        description: description
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displayAnalysisResult(data);
                    result.style.display = 'block';
                } else {
                    alert('分析失败: ' + data.error);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 显示分析结果
        function displayAnalysisResult(data) {
            const content = document.getElementById('analysisContent');

            const riskColor = getRiskColor(data.risk_level);

            content.innerHTML = `
                <div class="mb-3">
                    <h6><i class="fas fa-info-circle me-2"></i>工程参数</h6>
                    <ul class="list-unstyled ms-3">
                        <li><strong>工程类型:</strong> ${data.parameters.project_type}</li>
                        <li><strong>最大跨径:</strong> ${data.parameters.span_length}m</li>
                        <li><strong>梁高:</strong> ${data.parameters.beam_height}m</li>
                        <li><strong>桥宽:</strong> ${data.parameters.bridge_width}m</li>
                        <li><strong>支架高度:</strong> ${data.parameters.support_height}m</li>
                        <li><strong>环境因素:</strong> ${data.parameters.environmental_factors.join(', ') || '无特殊因素'}</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-cogs me-2"></i>推荐技术方案</h6>
                    <div class="ms-3">
                        <div class="mb-2">
                            <strong>支撑体系:</strong>
                            <ul class="list-unstyled ms-3">
                                <li>类型: ${data.solution.支撑体系.类型}</li>
                                <li>立杆间距: ${data.solution.支撑体系.立杆间距}</li>
                                <li>水平杆步距: ${data.solution.支撑体系.水平杆步距}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>风险评估</h6>
                    <span class="badge risk-badge ${riskColor}">${data.risk_level}</span>
                </div>
            `;
        }

        // 其他函数的简化版本
        async function reviewPlan() {
            alert('方案审查功能开发中...');
        }

        async function generateTemplate() {
            alert('模板生成功能开发中...');
        }

        function copyTemplate() {
            alert('复制功能开发中...');
        }

        // 获取风险等级对应的颜色类
        function getRiskColor(riskLevel) {
            switch (riskLevel) {
                case '低风险':
                    return 'bg-success';
                case '中等风险':
                    return 'bg-warning';
                case '高风险':
                    return 'bg-danger';
                case '重大风险':
                    return 'bg-dark';
                default:
                    return 'bg-secondary';
            }
        }
    </script>
</body>
</html>
