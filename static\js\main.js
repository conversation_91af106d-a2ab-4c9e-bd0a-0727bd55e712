// 施工方案智能体前端JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 工程参数分析
    document.getElementById('analyzeBtn').addEventListener('click', analyzeProject);
    
    // 方案审查
    document.getElementById('reviewBtn').addEventListener('click', reviewPlan);
    
    // 模板生成
    document.getElementById('generateBtn').addEventListener('click', generateTemplate);
    
    // 复制模板
    document.getElementById('copyTemplate').addEventListener('click', copyTemplate);
});

// 分析工程参数
async function analyzeProject() {
    const description = document.getElementById('projectDescription').value.trim();
    
    if (!description) {
        alert('请输入工程描述');
        return;
    }
    
    const btn = document.getElementById('analyzeBtn');
    const loading = document.getElementById('analysisLoading');
    const result = document.getElementById('analysisResult');
    
    // 显示加载状态
    btn.disabled = true;
    loading.style.display = 'block';
    result.style.display = 'none';
    
    try {
        const response = await fetch('/api/analyze_project', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayAnalysisResult(data);
            result.style.display = 'block';
        } else {
            alert('分析失败: ' + data.error);
        }
    } catch (error) {
        alert('请求失败: ' + error.message);
    } finally {
        btn.disabled = false;
        loading.style.display = 'none';
    }
}

// 显示分析结果
function displayAnalysisResult(data) {
    const content = document.getElementById('analysisContent');
    
    const riskColor = getRiskColor(data.risk_level);
    
    content.innerHTML = `
        <div class="mb-3">
            <h6><i class="fas fa-info-circle me-2"></i>工程参数</h6>
            <ul class="list-unstyled ms-3">
                <li><strong>工程类型:</strong> ${data.parameters.project_type}</li>
                <li><strong>最大跨径:</strong> ${data.parameters.span_length}m</li>
                <li><strong>梁高:</strong> ${data.parameters.beam_height}m</li>
                <li><strong>桥宽:</strong> ${data.parameters.bridge_width}m</li>
                <li><strong>支架高度:</strong> ${data.parameters.support_height}m</li>
                <li><strong>环境因素:</strong> ${data.parameters.environmental_factors.join(', ') || '无特殊因素'}</li>
            </ul>
        </div>
        
        <div class="mb-3">
            <h6><i class="fas fa-cogs me-2"></i>推荐技术方案</h6>
            <div class="ms-3">
                <div class="mb-2">
                    <strong>支撑体系:</strong>
                    <ul class="list-unstyled ms-3">
                        <li>类型: ${data.solution.支撑体系.类型}</li>
                        <li>立杆间距: ${data.solution.支撑体系.立杆间距}</li>
                        <li>水平杆步距: ${data.solution.支撑体系.水平杆步距}</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <strong>模板系统:</strong>
                    <ul class="list-unstyled ms-3">
                        <li>底模: ${data.solution.模板系统.底模}</li>
                        <li>侧模: ${data.solution.模板系统.侧模}</li>
                        <li>主龙骨: ${data.solution.模板系统.主龙骨}</li>
                        <li>次龙骨: ${data.solution.模板系统.次龙骨}</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>风险评估</h6>
            <span class="badge risk-badge ${riskColor}">${data.risk_level}</span>
        </div>
        
        ${data.solution.特殊措施.length > 0 ? `
        <div class="mb-3">
            <h6><i class="fas fa-tools me-2"></i>特殊措施</h6>
            <ul class="ms-3">
                ${data.solution.特殊措施.map(measure => `<li>${measure}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
    `;
}

// 审查施工方案
async function reviewPlan() {
    const content = document.getElementById('planContent').value.trim();
    const description = document.getElementById('planDescription').value.trim();
    
    if (!content) {
        alert('请输入施工方案内容');
        return;
    }
    
    if (!description) {
        alert('请输入工程描述');
        return;
    }
    
    const btn = document.getElementById('reviewBtn');
    const loading = document.getElementById('reviewLoading');
    const result = document.getElementById('reviewResult');
    
    // 显示加载状态
    btn.disabled = true;
    loading.style.display = 'block';
    result.style.display = 'none';
    
    try {
        const response = await fetch('/api/review_plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content: content,
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayReviewResult(data);
            result.style.display = 'block';
        } else {
            alert('审查失败: ' + data.error);
        }
    } catch (error) {
        alert('请求失败: ' + error.message);
    } finally {
        btn.disabled = false;
        loading.style.display = 'none';
    }
}

// 显示审查结果
function displayReviewResult(data) {
    const content = document.getElementById('reviewContent');
    
    const gradeColor = getGradeColor(data.review_result.grade);
    
    content.innerHTML = `
        <div class="mb-3">
            <h6><i class="fas fa-star me-2"></i>总体评价</h6>
            <div class="text-center">
                <div class="display-6 fw-bold ${gradeColor}">${data.review_result.total_score.toFixed(1)}</div>
                <div class="badge ${gradeColor} fs-6">${data.review_result.grade}</div>
            </div>
        </div>
        
        <div class="mb-3">
            <h6><i class="fas fa-chart-bar me-2"></i>详细评分</h6>
            <div class="ms-2">
                <div class="mb-2">
                    <small>完整性</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${data.review_result.detailed_scores.completeness}%">
                            ${data.review_result.detailed_scores.completeness.toFixed(1)}
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <small>技术性</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${data.review_result.detailed_scores.technical}%">
                            ${data.review_result.detailed_scores.technical.toFixed(1)}
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <small>安全性</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-warning" style="width: ${data.review_result.detailed_scores.safety}%">
                            ${data.review_result.detailed_scores.safety.toFixed(1)}
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <small>可行性</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-info" style="width: ${data.review_result.detailed_scores.feasibility}%">
                            ${data.review_result.detailed_scores.feasibility.toFixed(1)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        ${data.review_result.recommendations.length > 0 ? `
        <div class="mb-3">
            <h6><i class="fas fa-lightbulb me-2"></i>改进建议</h6>
            <ul class="list-unstyled ms-2">
                ${data.review_result.recommendations.map(rec => `
                    <li class="mb-1">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        ${rec}
                    </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.safety_issues.length > 0 ? `
        <div class="mb-3">
            <h6><i class="fas fa-shield-alt me-2 text-danger"></i>安全问题</h6>
            <ul class="list-unstyled ms-2">
                ${data.safety_issues.map(issue => `
                    <li class="mb-1 text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${issue}
                    </li>
                `).join('')}
            </ul>
        </div>
        ` : `
        <div class="mb-3">
            <h6><i class="fas fa-shield-alt me-2 text-success"></i>安全检查</h6>
            <p class="text-success ms-2">
                <i class="fas fa-check-circle me-2"></i>
                未发现明显安全问题
            </p>
        </div>
        `}
    `;
}

// 生成方案模板
async function generateTemplate() {
    const description = document.getElementById('templateDescription').value.trim();
    
    if (!description) {
        alert('请输入工程描述');
        return;
    }
    
    const btn = document.getElementById('generateBtn');
    const loading = document.getElementById('templateLoading');
    const result = document.getElementById('templateResult');
    
    // 显示加载状态
    btn.disabled = true;
    loading.style.display = 'block';
    result.style.display = 'none';
    
    try {
        const response = await fetch('/api/generate_template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('templateContent').textContent = data.template;
            result.style.display = 'block';
        } else {
            alert('生成失败: ' + data.error);
        }
    } catch (error) {
        alert('请求失败: ' + error.message);
    } finally {
        btn.disabled = false;
        loading.style.display = 'none';
    }
}

// 复制模板到剪贴板
function copyTemplate() {
    const content = document.getElementById('templateContent').textContent;
    
    navigator.clipboard.writeText(content).then(function() {
        const btn = document.getElementById('copyTemplate');
        const originalText = btn.innerHTML;
        
        btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-success');
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        alert('复制失败: ' + err);
    });
}

// 获取风险等级对应的颜色类
function getRiskColor(riskLevel) {
    switch (riskLevel) {
        case '低风险':
            return 'bg-success';
        case '中等风险':
            return 'bg-warning';
        case '高风险':
            return 'bg-danger';
        case '重大风险':
            return 'bg-dark';
        default:
            return 'bg-secondary';
    }
}

// 获取评级对应的颜色类
function getGradeColor(grade) {
    switch (grade) {
        case '优秀':
            return 'text-success';
        case '良好':
            return 'text-info';
        case '合格':
            return 'text-warning';
        case '不合格':
            return 'text-danger';
        default:
            return 'text-secondary';
    }
}
