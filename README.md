# 施工方案编制与审查智能体

基于京台4标箱梁模架专项施工方案学习开发的AI助手系统，能够自动分析工程参数、生成技术方案、审查安全措施。

## 🚀 功能特性

### 1. 智能参数识别
- 自动从工程描述中提取关键参数
- 识别跨径、梁高、桥宽等结构参数
- 分析环境因素（跨河、跨路、管线等）
- 判断工程类型和复杂程度

### 2. 技术方案生成
- 基于工程特点自动选择支撑体系
- 推荐合适的模板系统
- 设计基础处理方案
- 制定特殊部位处理措施

### 3. 安全审查评估
- 全面检查安全措施完整性
- 评估工程风险等级
- 识别潜在安全隐患
- 提供改进建议

### 4. 方案模板生成
- 自动生成标准化方案模板
- 包含完整的章节结构
- 填入具体的技术参数
- 符合规范要求

## 📋 系统架构

```
施工方案智能体系统
├── construction_plan_agent.py    # 核心智能体引擎
├── app.py                       # Flask Web应用
├── templates/
│   └── index.html              # 前端界面
├── static/
│   └── js/main.js              # 前端JavaScript
├── 技术标准知识库.json           # 技术标准数据库
├── 施工方案结构模板.md          # 方案结构模板
└── requirements.txt            # Python依赖
```

## 🛠️ 安装与运行

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd consolution

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python app.py
```

### 3. 访问系统
打开浏览器访问：http://localhost:5000

## 📖 使用指南

### 工程参数分析
1. 在"工程描述输入"框中输入工程信息
2. 点击"开始分析"按钮
3. 系统将自动提取参数并生成技术方案
4. 查看风险评估结果

**示例输入：**
```
京台高速公路现浇箱梁工程，跨径35+38+35m，梁高2.0m，桥宽42m，
上跨现况道路，需设置门洞保证交通。地质条件为粉砂土层。
```

### 方案审查评估
1. 在"施工方案内容"框中粘贴方案文本
2. 在"工程描述"框中输入工程信息
3. 点击"开始审查"按钮
4. 查看评分结果和改进建议

### 方案模板生成
1. 输入工程描述信息
2. 点击"生成模板"按钮
3. 系统生成完整的方案模板
4. 可以复制模板进行进一步编辑

## 🧠 核心算法

### 参数识别算法
- 使用正则表达式匹配数值参数
- 关键词匹配识别工程类型
- 环境因素自动分类

### 技术方案选择
- 基于工程规模选择支撑体系
- 根据跨径和高度确定构造要求
- 环境因素驱动的特殊措施设计

### 安全风险评估
- 多维度风险评分模型
- 基于规范要求的安全检查
- 智能化改进建议生成

### 方案审查评分
- 完整性检查（30%权重）
- 技术合规性（30%权重）
- 安全充分性（25%权重）
- 施工可行性（15%权重）

## 📚 技术标准库

系统内置了完整的技术标准知识库，包括：

### 国家标准
- GB50204-2015 混凝土结构工程施工质量验收规范
- GB50666-2011 混凝土结构工程施工规范
- GB15831-2006 钢管脚手架扣件

### 行业标准
- JGJ162-2008 建筑施工模板安全技术规范
- JGJ166-2008 建筑施工碗扣式钢管脚手架安全技术规范
- JGJ130-2011 建筑施工扣件式钢管脚手架安全技术规范

### 安全法规
- 建质[2009]87号 危险性较大的分部分项工程安全管理办法
- 建质[2009]254号 高大模板支撑系统施工安全监督管理导则

## 🔧 扩展开发

### 添加新的工程类型
在 `construction_plan_agent.py` 中的 `ProjectType` 枚举中添加新类型：

```python
class ProjectType(Enum):
    BRIDGE_BOX_GIRDER = "现浇箱梁"
    BRIDGE_SLAB = "现浇板梁"
    NEW_TYPE = "新工程类型"  # 添加新类型
```

### 扩展技术标准库
编辑 `技术标准知识库.json` 文件，添加新的标准规范。

### 自定义审查规则
在 `ReviewEngine` 类中修改审查逻辑和评分权重。

## 📊 系统特点

### 学习能力
- 基于真实施工方案学习
- 提取行业最佳实践
- 持续优化算法模型

### 智能化程度
- 自然语言理解
- 参数自动提取
- 智能方案生成

### 实用性
- 符合行业规范
- 贴近实际工程
- 提高工作效率

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意：** 本系统生成的方案仅供参考，实际使用时请结合具体工程情况和相关规范要求进行调整完善。
