#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Simple Icon for Construction Plan Agent
"""

def create_simple_icon():
    """Create a simple text-based icon"""
    
    # Create a simple batch file that sets its own icon
    icon_bat = """@echo off
title Construction Plan Agent
echo Starting Construction Plan Agent...
call start_app.bat
"""
    
    with open('Construction_Plan_Agent.bat', 'w', encoding='utf-8') as f:
        f.write(icon_bat)
    
    print("✅ Created Construction_Plan_Agent.bat")
    
    # Create a VBS script to create desktop shortcut
    vbs_script = '''
Set oWS = WScript.CreateObject("WScript.Shell")
sLinkFile = oWS.SpecialFolders("Desktop") & "\\Construction Plan Agent.lnk"
Set oLink = oWS.CreateShortcut(sLinkFile)
oLink.TargetPath = WScript.ScriptFullName.Replace(WScript.ScriptName, "start_app.bat")
oLink.WorkingDirectory = WScript.ScriptFullName.Replace("\\" & WScript.ScriptName, "")
oLink.Description = "Construction Plan Agent - One Click Start"
oLink.Save
WScript.Echo "Desktop shortcut created successfully!"
'''
    
    with open('create_shortcut.vbs', 'w', encoding='utf-8') as f:
        f.write(vbs_script)
    
    print("✅ Created create_shortcut.vbs")
    
    # Create installation instructions
    instructions = """
# Desktop Shortcut Installation

## Quick Setup:

1. Double-click: create_shortcut.vbs
2. A desktop shortcut will be created
3. Double-click the desktop shortcut to start the app

## Manual Setup:

1. Right-click on desktop → New → Shortcut
2. Browse and select: start_app.bat
3. Name it: Construction Plan Agent
4. Click Finish

## Usage:

- Double-click the desktop shortcut
- Wait for the app to start (3-5 seconds)
- Browser will open automatically
- Close the command window to stop

## Web Address:
http://127.0.0.1:5004
"""
    
    with open('Desktop_Shortcut_Instructions.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Created Desktop_Shortcut_Instructions.txt")
    print("\n🎉 Setup files created!")
    print("\nNext steps:")
    print("1. Double-click: create_shortcut.vbs")
    print("2. Use the desktop shortcut to start the app")

if __name__ == "__main__":
    create_simple_icon()
