#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
施工方案编制与审查智能体
Construction Plan Compilation and Review Agent

基于京台4标箱梁模架专项施工方案学习开发的智能体系统
"""

import json
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import math

class ProjectType(Enum):
    """工程类型枚举"""
    BRIDGE_BOX_GIRDER = "现浇箱梁"
    BRIDGE_SLAB = "现浇板梁"
    BUILDING_FRAME = "框架结构"
    BUILDING_SHEAR_WALL = "剪力墙结构"

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "低风险"
    MEDIUM = "中等风险"
    HIGH = "高风险"
    CRITICAL = "重大风险"

@dataclass
class ProjectParameters:
    """工程参数数据类"""
    project_name: str
    project_type: ProjectType
    span_length: float  # 跨径(m)
    beam_height: float  # 梁高(m)
    bridge_width: float  # 桥宽(m)
    support_height: float  # 支架高度(m)
    geological_conditions: str  # 地质条件
    environmental_factors: List[str]  # 环境因素

class ConstructionPlanAgent:
    """施工方案编制与审查智能体"""
    
    def __init__(self):
        """初始化智能体"""
        self.standards_db = self._load_standards_database()
        self.template = self._load_plan_template()
        self.calculation_engine = CalculationEngine()
        self.safety_checker = SafetyChecker()
        self.review_engine = ReviewEngine()
    
    def _load_standards_database(self) -> Dict:
        """加载技术标准数据库"""
        try:
            with open('技术标准知识库.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def _load_plan_template(self) -> Dict:
        """加载方案模板"""
        # 基于施工方案结构模板.md的内容
        return {
            "编制依据": ["国家标准", "行业标准", "地方标准", "设计文件"],
            "工程概况": ["总体概况", "结构概况", "现场条件", "施工部署"],
            "技术方案选择": ["选择原则", "方案比选", "材料选择"],
            "详细设计": ["基础处理", "支撑体系", "模板设计", "特殊部位"],
            "施工工艺": ["技术准备", "材料准备", "施工流程", "技术要求"],
            "安全保证措施": ["管理体系", "专项措施", "个人防护", "环境安全"],
            "应急预案": ["风险识别", "组织机构", "应急措施", "应急资源"],
            "计算验证": ["荷载计算", "结构验算", "地基承载力", "特殊结构"]
        }
    
    def extract_project_parameters(self, project_description: str) -> ProjectParameters:
        """从工程描述中提取关键参数"""
        # 使用正则表达式和关键词匹配提取参数
        
        # 提取跨径信息
        span_pattern = r'(\d+(?:\.\d+)?)\s*[+×x]\s*(\d+(?:\.\d+)?)'
        spans = re.findall(span_pattern, project_description)
        max_span = 0
        if spans:
            for span_group in spans:
                for span in span_group:
                    max_span = max(max_span, float(span))
        
        # 提取梁高信息
        height_pattern = r'梁高[：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
        height_match = re.search(height_pattern, project_description)
        beam_height = float(height_match.group(1)) if height_match else 2.0
        
        # 提取桥宽信息
        width_pattern = r'[桥宽|宽度][：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
        width_match = re.search(width_pattern, project_description)
        bridge_width = float(width_match.group(1)) if width_match else 20.0
        
        # 判断工程类型
        project_type = ProjectType.BRIDGE_BOX_GIRDER
        if '箱梁' in project_description:
            project_type = ProjectType.BRIDGE_BOX_GIRDER
        elif '板梁' in project_description or '实体板' in project_description:
            project_type = ProjectType.BRIDGE_SLAB
        
        # 提取环境因素
        environmental_factors = []
        if '跨河' in project_description or '跨渠' in project_description:
            environmental_factors.append('水体跨越')
        if '跨路' in project_description:
            environmental_factors.append('道路跨越')
        if '管线' in project_description:
            environmental_factors.append('管线保护')
        
        return ProjectParameters(
            project_name="待提取",
            project_type=project_type,
            span_length=max_span,
            beam_height=beam_height,
            bridge_width=bridge_width,
            support_height=beam_height + 5.0,  # 估算支架高度
            geological_conditions="待分析",
            environmental_factors=environmental_factors
        )
    
    def generate_technical_solution(self, params: ProjectParameters) -> Dict:
        """基于工程参数生成技术方案"""
        solution = {
            "支撑体系": self._select_support_system(params),
            "模板系统": self._select_formwork_system(params),
            "基础处理": self._design_foundation(params),
            "特殊措施": self._design_special_measures(params)
        }
        return solution
    
    def _select_support_system(self, params: ProjectParameters) -> Dict:
        """选择支撑体系"""
        if params.span_length > 30 or params.support_height > 8:
            return {
                "类型": "碗扣式满堂红脚手架",
                "立杆间距": "腹板处60cm，其他90cm",
                "水平杆步距": "120cm",
                "剪刀撑": "纵横向每4.5m设置",
                "特殊要求": "高大模板需专家论证"
            }
        else:
            return {
                "类型": "扣件式脚手架",
                "立杆间距": "90cm×90cm",
                "水平杆步距": "120cm",
                "剪刀撑": "按规范设置"
            }
    
    def _select_formwork_system(self, params: ProjectParameters) -> Dict:
        """选择模板系统"""
        return {
            "底模": "1.5cm清水模板",
            "侧模": "1.5cm清水模板",
            "主龙骨": "10#槽钢",
            "次龙骨": "10×10cm方木",
            "倒角模板": "定型钢模"
        }
    
    def _design_foundation(self, params: ProjectParameters) -> Dict:
        """设计基础处理方案"""
        return {
            "地基处理": "整平压实，压实度≥90%",
            "垫层": "15cm二灰碎石，压实度≥95%",
            "排水": "设置0.5%横坡和排水沟",
            "承载力要求": "≥160kPa"
        }
    
    def _design_special_measures(self, params: ProjectParameters) -> List[str]:
        """设计特殊措施"""
        measures = []
        
        if '水体跨越' in params.environmental_factors:
            measures.append("采用贝雷片跨越水体")
        
        if '道路跨越' in params.environmental_factors:
            measures.append("设置门洞保证交通")
        
        if '管线保护' in params.environmental_factors:
            measures.append("管线保护专项措施")
        
        if params.span_length > 40:
            measures.append("大跨度结构专项设计")
        
        return measures

class CalculationEngine:
    """计算验证引擎"""
    
    def __init__(self):
        self.material_properties = {
            'concrete': {'density': 25, 'fc': 20},  # kN/m³, MPa
            'steel': {'fy': 205, 'E': 2.06e5},      # MPa
            'wood': {'fm': 14.5, 'E': 1.1e4}       # MPa
        }
    
    def calculate_loads(self, params: ProjectParameters) -> Dict:
        """计算荷载"""
        # 恒载计算
        dead_load = {
            'formwork': 0.5,  # kPa
            'concrete': params.beam_height * 25,  # kPa
            'reinforcement': 1.0  # kPa
        }
        
        # 活载计算
        live_load = {
            'construction': 2.5,  # kPa
            'vibration': 2.0,     # kPa
            'equipment': 1.0      # kPa
        }
        
        return {'dead_load': dead_load, 'live_load': live_load}
    
    def verify_strength(self, loads: Dict, member_properties: Dict) -> bool:
        """验证强度"""
        # 简化的强度验算
        total_load = sum(loads['dead_load'].values()) * 1.2 + sum(loads['live_load'].values()) * 1.4
        allowable_stress = member_properties.get('allowable_stress', 205)  # MPa
        
        # 这里需要根据具体构件进行详细计算
        return True  # 简化返回
    
    def verify_deflection(self, loads: Dict, member_properties: Dict) -> bool:
        """验证挠度"""
        # 简化的挠度验算
        return True  # 简化返回

class SafetyChecker:
    """安全措施检查器"""
    
    def __init__(self):
        self.safety_requirements = {
            'high_work': ['安全带', '防护栏', '安全网'],
            'electrical': ['漏电保护', '接地保护', '绝缘措施'],
            'fire_prevention': ['消防器材', '动火许可', '易燃品管理'],
            'mechanical': ['设备检查', '操作培训', '维护保养']
        }
    
    def check_safety_measures(self, plan_content: str) -> List[str]:
        """检查安全措施完整性"""
        missing_measures = []
        
        for category, measures in self.safety_requirements.items():
            for measure in measures:
                if measure not in plan_content:
                    missing_measures.append(f"{category}: 缺少{measure}相关措施")
        
        return missing_measures
    
    def assess_risk_level(self, params: ProjectParameters) -> RiskLevel:
        """评估风险等级"""
        risk_score = 0
        
        # 根据跨径评分
        if params.span_length > 50:
            risk_score += 3
        elif params.span_length > 30:
            risk_score += 2
        elif params.span_length > 20:
            risk_score += 1
        
        # 根据高度评分
        if params.support_height > 10:
            risk_score += 3
        elif params.support_height > 8:
            risk_score += 2
        elif params.support_height > 5:
            risk_score += 1
        
        # 根据环境因素评分
        risk_score += len(params.environmental_factors)
        
        if risk_score >= 6:
            return RiskLevel.CRITICAL
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

class ReviewEngine:
    """方案审查引擎"""
    
    def __init__(self):
        self.review_criteria = {
            'completeness': 0.3,    # 完整性权重
            'technical': 0.3,       # 技术性权重
            'safety': 0.25,         # 安全性权重
            'feasibility': 0.15     # 可行性权重
        }
    
    def review_plan(self, plan_content: str, params: ProjectParameters) -> Dict:
        """审查施工方案"""
        scores = {
            'completeness': self._check_completeness(plan_content),
            'technical': self._check_technical_compliance(plan_content),
            'safety': self._check_safety_adequacy(plan_content),
            'feasibility': self._check_feasibility(plan_content, params)
        }
        
        total_score = sum(score * weight for score, weight in 
                         zip(scores.values(), self.review_criteria.values()))
        
        return {
            'total_score': total_score,
            'detailed_scores': scores,
            'grade': self._get_grade(total_score),
            'recommendations': self._generate_recommendations(scores)
        }
    
    def _check_completeness(self, content: str) -> float:
        """检查完整性"""
        required_sections = ['编制依据', '工程概况', '技术方案', '安全措施', '计算书']
        present_sections = sum(1 for section in required_sections if section in content)
        return present_sections / len(required_sections) * 100
    
    def _check_technical_compliance(self, content: str) -> float:
        """检查技术合规性"""
        # 检查是否包含必要的技术标准引用
        standards = ['GB50204', 'JGJ162', 'JGJ166']
        compliance_score = sum(10 for std in standards if std in content)
        return min(compliance_score, 100)
    
    def _check_safety_adequacy(self, content: str) -> float:
        """检查安全措施充分性"""
        safety_keywords = ['安全带', '防护栏', '安全网', '应急预案', '安全教育']
        safety_score = sum(20 for keyword in safety_keywords if keyword in content)
        return min(safety_score, 100)
    
    def _check_feasibility(self, content: str, params: ProjectParameters) -> float:
        """检查可行性"""
        # 简化的可行性检查
        return 85.0  # 默认分数
    
    def _get_grade(self, score: float) -> str:
        """获取评级"""
        if score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "合格"
        else:
            return "不合格"
    
    def _generate_recommendations(self, scores: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if scores['completeness'] < 80:
            recommendations.append("补充完善方案章节内容")
        
        if scores['technical'] < 80:
            recommendations.append("增加技术标准依据和计算验证")
        
        if scores['safety'] < 80:
            recommendations.append("完善安全措施和应急预案")
        
        if scores['feasibility'] < 80:
            recommendations.append("优化施工工艺和资源配置")
        
        return recommendations

# 使用示例
if __name__ == "__main__":
    # 创建智能体实例
    agent = ConstructionPlanAgent()
    
    # 示例工程描述
    project_desc = """
    京台高速公路现浇箱梁工程，跨径35+38+35m，梁高2.0m，桥宽42m，
    上跨现况道路，需设置门洞保证交通。地质条件为粉砂土层。
    """
    
    # 提取工程参数
    params = agent.extract_project_parameters(project_desc)
    print(f"工程参数: {params}")
    
    # 生成技术方案
    solution = agent.generate_technical_solution(params)
    print(f"技术方案: {solution}")
    
    # 风险评估
    risk_level = agent.safety_checker.assess_risk_level(params)
    print(f"风险等级: {risk_level.value}")
