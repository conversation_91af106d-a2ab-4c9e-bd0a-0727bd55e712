@echo off
chcp 65001 >nul
title 施工方案智能体 - 桌面快捷方式安装

echo.
echo ========================================
echo   施工方案智能体 - 桌面快捷方式安装
echo ========================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python环境
    echo 请先安装Python后再运行此脚本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:: 安装必要的依赖包
echo 📦 正在安装依赖包...
pip install Pillow pywin32 >nul 2>&1

:: 创建图标
echo 🎨 正在创建应用图标...
python create_icon.py

:: 创建快捷方式
echo 🔗 正在创建桌面快捷方式...
python 创建桌面快捷方式.py

echo.
echo ✅ 安装完成！
echo.
echo 现在您可以通过以下方式启动应用：
echo 1. 双击桌面上的"施工方案智能体"图标
echo 2. 或者双击"启动施工方案智能体.bat"文件
echo.
pause
