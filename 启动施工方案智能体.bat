@echo off
chcp 65001 >nul
title 施工方案编制与审查智能体

echo.
echo ========================================
echo    施工方案编制与审查智能体
echo    Construction Plan Agent
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python环境
    echo 请先安装Python 3.7或更高版本
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
echo ✅ Python环境检查通过
for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo    版本: %%i
echo.

:: 切换到应用目录
cd /d "%~dp0"
echo 📁 当前目录: %CD%
echo.

:: 检查必要文件
if not exist "complete_app.py" (
    echo ❌ 错误：找不到应用文件 complete_app.py
    echo 请确保所有文件都在正确位置
    echo.
    pause
    exit /b 1
)

:: 检查依赖包
echo 🔍 检查依赖包...
python -c "import flask, pandas, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  正在安装缺失的依赖包...
    pip install flask pandas openpyxl
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请手动运行: pip install flask pandas openpyxl
        echo.
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.

:: 启动应用
echo 🚀 正在启动施工方案智能体...
echo.
echo 启动完成后将自动打开浏览器
echo 访问地址: http://127.0.0.1:5004
echo.
echo 按 Ctrl+C 可停止服务
echo ========================================
echo.

:: 启动Python应用
start "" python complete_app.py

:: 等待服务启动
timeout /t 3 /nobreak >nul

:: 自动打开浏览器
start "" http://127.0.0.1:5004

:: 保持窗口打开
echo.
echo 🌐 浏览器已打开，服务正在后台运行...
echo.
echo 如需停止服务，请关闭此窗口或按 Ctrl+C
pause >nul
