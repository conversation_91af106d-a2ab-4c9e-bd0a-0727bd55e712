#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理工具
Knowledge Base Management Tool
"""

import json
import os
import requests
from pathlib import Path
from typing import Dict, List
import pandas as pd
from datetime import datetime
import logging

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self, kb_path: str = "knowledge_base"):
        self.kb_path = Path(kb_path)
        self.kb_path.mkdir(exist_ok=True)
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('knowledge_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def collect_standards_from_web(self):
        """从网络收集标准规范"""
        standards_sources = [
            {
                "name": "国家标准全文公开系统",
                "url": "http://openstd.samr.gov.cn/",
                "categories": ["建筑工程", "安全生产"]
            },
            {
                "name": "工程建设标准化信息网",
                "url": "http://www.ccsn.gov.cn/",
                "categories": ["建筑施工", "结构设计"]
            }
        ]
        
        collected_standards = []
        
        for source in standards_sources:
            self.logger.info(f"正在收集来源: {source['name']}")
            # 这里需要实现具体的网络爬虫逻辑
            # 由于涉及具体网站结构，这里提供框架
            try:
                standards = self.scrape_standards(source)
                collected_standards.extend(standards)
                self.logger.info(f"从 {source['name']} 收集到 {len(standards)} 个标准")
            except Exception as e:
                self.logger.error(f"收集失败: {e}")
        
        return collected_standards
    
    def scrape_standards(self, source: Dict) -> List[Dict]:
        """爬取标准规范（示例实现）"""
        # 这里是示例实现，实际需要根据具体网站调整
        standards = []
        
        # 模拟收集的标准数据
        mock_standards = [
            {
                "code": "GB50010-2010",
                "title": "混凝土结构设计规范",
                "category": "结构设计",
                "status": "现行",
                "publish_date": "2010-08-18",
                "implement_date": "2011-07-01",
                "content_summary": "规定了混凝土结构设计的基本原则、材料性能、计算方法等"
            },
            {
                "code": "JGJ162-2008",
                "title": "建筑施工模板安全技术规范",
                "category": "施工安全",
                "status": "现行",
                "publish_date": "2008-06-25",
                "implement_date": "2008-12-01",
                "content_summary": "规定了建筑施工中模板工程的安全技术要求"
            }
        ]
        
        return mock_standards
    
    def import_project_cases(self, cases_file: str):
        """导入工程案例"""
        try:
            if cases_file.endswith('.xlsx') or cases_file.endswith('.xls'):
                df = pd.read_excel(cases_file)
            elif cases_file.endswith('.csv'):
                df = pd.read_csv(cases_file)
            else:
                raise ValueError("不支持的文件格式")
            
            cases = []
            for _, row in df.iterrows():
                case = {
                    "case_id": row.get('案例编号', ''),
                    "project_name": row.get('工程名称', ''),
                    "project_type": row.get('工程类型', ''),
                    "location": row.get('工程地点', ''),
                    "parameters": {
                        "spans": self.parse_spans(row.get('跨径', '')),
                        "beam_height": float(row.get('梁高', 0)),
                        "bridge_width": float(row.get('桥宽', 0))
                    },
                    "solution": {
                        "support_system": row.get('支撑体系', ''),
                        "formwork": row.get('模板系统', ''),
                        "foundation": row.get('基础处理', '')
                    },
                    "construction_period": row.get('施工工期', ''),
                    "cost": row.get('工程造价', ''),
                    "quality_grade": row.get('质量等级', ''),
                    "lessons_learned": row.get('经验教训', '').split('；') if row.get('经验教训') else []
                }
                cases.append(case)
            
            # 保存案例数据
            cases_file_path = self.kb_path / "project_cases.json"
            with open(cases_file_path, 'w', encoding='utf-8') as f:
                json.dump(cases, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功导入 {len(cases)} 个工程案例")
            return cases
            
        except Exception as e:
            self.logger.error(f"导入案例失败: {e}")
            return []
    
    def parse_spans(self, span_str: str) -> List[float]:
        """解析跨径字符串"""
        if not span_str:
            return []
        
        # 处理各种跨径表示格式
        span_str = str(span_str).replace('m', '').replace('米', '')
        
        if '+' in span_str:
            return [float(x.strip()) for x in span_str.split('+')]
        elif '×' in span_str:
            parts = span_str.split('×')
            if len(parts) == 2:
                count, span = parts
                return [float(span.strip())] * int(count.strip())
        else:
            try:
                return [float(span_str.strip())]
            except:
                return []
    
    def extract_knowledge_from_documents(self, doc_path: str):
        """从文档中提取知识"""
        knowledge_items = []
        
        try:
            # 读取文档内容
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取章节标题和内容
            sections = self.extract_sections(content)
            
            for section in sections:
                knowledge_item = {
                    "id": f"DOC_{datetime.now().strftime('%Y%m%d%H%M%S')}_{len(knowledge_items)}",
                    "type": "document_section",
                    "title": section['title'],
                    "content": section['content'],
                    "source": doc_path,
                    "extract_date": datetime.now().isoformat(),
                    "keywords": self.extract_keywords(section['content'])
                }
                knowledge_items.append(knowledge_item)
            
            # 保存提取的知识
            knowledge_file = self.kb_path / f"extracted_knowledge_{datetime.now().strftime('%Y%m%d')}.json"
            with open(knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(knowledge_items, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"从文档 {doc_path} 提取了 {len(knowledge_items)} 个知识项")
            return knowledge_items
            
        except Exception as e:
            self.logger.error(f"文档知识提取失败: {e}")
            return []
    
    def extract_sections(self, content: str) -> List[Dict]:
        """提取文档章节"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测章节标题（简单规则）
            if (line.startswith('#') or 
                any(char in line for char in ['一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、']) or
                line.endswith('：') or line.endswith(':')):
                
                # 保存前一个章节
                if current_section and current_content:
                    sections.append({
                        'title': current_section,
                        'content': '\n'.join(current_content)
                    })
                
                # 开始新章节
                current_section = line.replace('#', '').strip()
                current_content = []
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            sections.append({
                'title': current_section,
                'content': '\n'.join(current_content)
            })
        
        return sections
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 建筑工程相关关键词
        construction_keywords = [
            '混凝土', '钢筋', '模板', '脚手架', '支撑', '基础', '梁', '柱', '板',
            '安全', '质量', '施工', '设计', '计算', '验收', '检查', '监理',
            '跨径', '梁高', '桥宽', '荷载', '强度', '刚度', '稳定性', '挠度',
            '规范', '标准', '要求', '措施', '方案', '工艺', '技术', '方法'
        ]
        
        found_keywords = []
        for keyword in construction_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def validate_knowledge_consistency(self):
        """验证知识一致性"""
        inconsistencies = []
        
        # 检查标准规范的一致性
        standards_file = self.kb_path / "standards.json"
        if standards_file.exists():
            with open(standards_file, 'r', encoding='utf-8') as f:
                standards = json.load(f)
            
            # 检查重复标准
            codes = [std['code'] for std in standards]
            duplicates = [code for code in set(codes) if codes.count(code) > 1]
            
            if duplicates:
                inconsistencies.append({
                    'type': 'duplicate_standards',
                    'items': duplicates
                })
        
        # 检查案例数据的一致性
        cases_file = self.kb_path / "project_cases.json"
        if cases_file.exists():
            with open(cases_file, 'r', encoding='utf-8') as f:
                cases = json.load(f)
            
            # 检查参数合理性
            for case in cases:
                params = case.get('parameters', {})
                if params.get('beam_height', 0) > 5:  # 梁高超过5米可能不合理
                    inconsistencies.append({
                        'type': 'unreasonable_parameter',
                        'case_id': case.get('case_id'),
                        'issue': f"梁高 {params['beam_height']}m 可能过大"
                    })
        
        return inconsistencies
    
    def generate_knowledge_report(self):
        """生成知识库报告"""
        report = {
            'generation_time': datetime.now().isoformat(),
            'statistics': {},
            'quality_metrics': {},
            'recommendations': []
        }
        
        # 统计信息
        kb_files = list(self.kb_path.glob('*.json'))
        report['statistics']['total_files'] = len(kb_files)
        
        total_items = 0
        for file_path in kb_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        total_items += len(data)
                    else:
                        total_items += 1
            except:
                continue
        
        report['statistics']['total_knowledge_items'] = total_items
        
        # 质量指标
        inconsistencies = self.validate_knowledge_consistency()
        report['quality_metrics']['inconsistencies_count'] = len(inconsistencies)
        report['quality_metrics']['consistency_score'] = max(0, 100 - len(inconsistencies) * 10)
        
        # 建议
        if len(inconsistencies) > 0:
            report['recommendations'].append("发现数据不一致问题，建议进行数据清理")
        
        if total_items < 100:
            report['recommendations'].append("知识库规模较小，建议增加更多案例和标准")
        
        # 保存报告
        report_file = self.kb_path / f"knowledge_report_{datetime.now().strftime('%Y%m%d')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"知识库报告已生成: {report_file}")
        return report
    
    def backup_knowledge_base(self):
        """备份知识库"""
        import shutil
        
        backup_dir = Path(f"knowledge_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        backup_dir.mkdir(exist_ok=True)
        
        # 复制所有知识库文件
        for file_path in self.kb_path.glob('*'):
            if file_path.is_file():
                shutil.copy2(file_path, backup_dir)
        
        self.logger.info(f"知识库已备份到: {backup_dir}")
        return backup_dir

# 使用示例
if __name__ == "__main__":
    manager = KnowledgeManager()
    
    # 生成知识库报告
    report = manager.generate_knowledge_report()
    print("知识库报告:", json.dumps(report, ensure_ascii=False, indent=2))
    
    # 备份知识库
    backup_dir = manager.backup_knowledge_base()
    print(f"备份完成: {backup_dir}")
    
    # 验证一致性
    inconsistencies = manager.validate_knowledge_consistency()
    print(f"发现 {len(inconsistencies)} 个不一致问题")
