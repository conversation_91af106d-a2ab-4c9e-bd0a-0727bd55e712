#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版施工方案智能体Web应用
"""

from flask import Flask, render_template_string, request, jsonify
import re

app = Flask(__name__)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工方案编制与审查智能体</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .result-card {
            margin-top: 2rem;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                🏗️ 施工方案智能体
            </a>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">施工方案编制与审查智能体</h1>
            <p class="lead mb-4">基于京台4标箱梁模架专项施工方案学习开发的AI助手</p>
            <p class="mb-0">自动分析工程参数 • 智能生成技术方案 • 全面审查安全措施</p>
        </div>
    </section>

    <!-- 主要功能区域 -->
    <section class="py-5">
        <div class="container">
            <!-- 功能选项卡 -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="analyze-tab" data-bs-toggle="tab" data-bs-target="#analyze" type="button" role="tab">
                        🔍 参数分析
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="generate-tab" data-bs-toggle="tab" data-bs-target="#generate" type="button" role="tab">
                        📝 方案生成
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="review-tab" data-bs-toggle="tab" data-bs-target="#review" type="button" role="tab">
                        ✅方案审查
                    </button>
                </li>
            </ul>

            <!-- 选项卡内容 -->
            <div class="tab-content" id="mainTabContent">
                <!-- 参数分析选项卡 -->
                <div class="tab-pane fade show active" id="analyze" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">🔍 工程参数分析</h5>
                                </div>
                                <div class="card-body">
                                    <textarea id="projectDescription" class="form-control" rows="6"
                                        placeholder="请输入工程描述，例如：&#10;京台高速公路现浇箱梁工程，跨径35+38+35m，梁高2.0m，桥宽42m，上跨现况道路，需设置门洞保证交通。地质条件为粉砂土层。"></textarea>
                                    <button id="analyzeBtn" class="btn btn-primary mt-3" onclick="analyzeProject()">
                                        开始分析
                                    </button>
                                    <div id="loading" style="display: none;" class="mt-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">分析中...</span>
                                        </div>
                                        <span class="ms-2">正在分析工程参数...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div id="resultCard" class="card result-card" style="display: none;">
                                <div class="card-header">
                                    <h5 class="mb-0">📊 分析结果</h5>
                                </div>
                                <div class="card-body">
                                    <div id="resultContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 方案生成选项卡 -->
                <div class="tab-pane fade" id="generate" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">📝 方案生成</h5>
                                </div>
                                <div class="card-body">
                                    <textarea id="generateDescription" class="form-control" rows="6"
                                        placeholder="请输入工程描述，系统将自动生成完整的施工方案..."></textarea>
                                    <button id="generateBtn" class="btn btn-success mt-3" onclick="generatePlan()">
                                        生成方案
                                    </button>
                                    <div id="generateLoading" style="display: none;" class="mt-3">
                                        <div class="spinner-border text-success" role="status">
                                            <span class="visually-hidden">生成中...</span>
                                        </div>
                                        <span class="ms-2">正在生成施工方案...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-8">
                            <div id="planCard" class="card result-card" style="display: none;">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">📋 生成的施工方案</h5>
                                    <button id="copyPlan" class="btn btn-sm btn-outline-primary" onclick="copyPlan()">
                                        📋 复制方案
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="planContent" style="max-height: 500px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; background-color: #f8f9fa; padding: 1rem; border-radius: 5px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 方案审查选项卡 -->
                <div class="tab-pane fade" id="review" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">✅ 方案审查</h5>
                                </div>
                                <div class="card-body">
                                    <textarea id="reviewContent" class="form-control" rows="10"
                                        placeholder="请粘贴施工方案内容进行审查..."></textarea>
                                    <button id="reviewBtn" class="btn btn-warning mt-3" onclick="reviewPlan()">
                                        开始审查
                                    </button>
                                    <div id="reviewLoading" style="display: none;" class="mt-3">
                                        <div class="spinner-border text-warning" role="status">
                                            <span class="visually-hidden">审查中...</span>
                                        </div>
                                        <span class="ms-2">正在审查方案...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div id="reviewCard" class="card result-card" style="display: none;">
                                <div class="card-header">
                                    <h5 class="mb-0">📊 审查结果</h5>
                                </div>
                                <div class="card-body">
                                    <div id="reviewResult"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2024 施工方案编制与审查智能体 - 基于AI技术的建筑施工辅助系统</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function analyzeProject() {
            const description = document.getElementById('projectDescription').value.trim();
            
            if (!description) {
                alert('请输入工程描述');
                return;
            }
            
            const btn = document.getElementById('analyzeBtn');
            const loading = document.getElementById('loading');
            const resultCard = document.getElementById('resultCard');
            
            // 显示加载状态
            btn.disabled = true;
            loading.style.display = 'block';
            resultCard.style.display = 'none';
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        description: description
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResult(data);
                    resultCard.style.display = 'block';
                } else {
                    alert('分析失败: ' + data.error);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        function displayResult(data) {
            const content = document.getElementById('resultContent');
            
            content.innerHTML = `
                <div class="mb-3">
                    <h6>🏗️ 工程参数</h6>
                    <ul class="list-unstyled ms-3">
                        <li><strong>工程类型:</strong> ${data.project_type}</li>
                        <li><strong>最大跨径:</strong> ${data.span_length}m</li>
                        <li><strong>梁高:</strong> ${data.beam_height}m</li>
                        <li><strong>桥宽:</strong> ${data.bridge_width}m</li>
                        <li><strong>环境因素:</strong> ${data.environmental_factors.join(', ') || '无特殊因素'}</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>⚙️ 推荐方案</h6>
                    <div class="ms-3">
                        <p><strong>支撑体系:</strong> ${data.support_system}</p>
                        <p><strong>模板系统:</strong> ${data.formwork_system}</p>
                        <p><strong>基础处理:</strong> ${data.foundation}</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>⚠️ 风险评估</h6>
                    <span class="badge ${getRiskColor(data.risk_level)} fs-6">${data.risk_level}</span>
                </div>
                
                ${data.special_measures.length > 0 ? `
                <div class="mb-3">
                    <h6>🔧 特殊措施</h6>
                    <ul class="ms-3">
                        ${data.special_measures.map(measure => `<li>${measure}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
            `;
        }

        // 生成施工方案
        async function generatePlan() {
            const description = document.getElementById('generateDescription').value.trim();

            if (!description) {
                alert('请输入工程描述');
                return;
            }

            const btn = document.getElementById('generateBtn');
            const loading = document.getElementById('generateLoading');
            const planCard = document.getElementById('planCard');

            // 显示加载状态
            btn.disabled = true;
            loading.style.display = 'block';
            planCard.style.display = 'none';

            try {
                const response = await fetch('/api/generate_plan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        description: description
                    })
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('planContent').textContent = data.plan;
                    planCard.style.display = 'block';
                } else {
                    alert('生成失败: ' + data.error);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 审查施工方案
        async function reviewPlan() {
            const content = document.getElementById('reviewContent').value.trim();

            if (!content) {
                alert('请输入方案内容');
                return;
            }

            const btn = document.getElementById('reviewBtn');
            const loading = document.getElementById('reviewLoading');
            const reviewCard = document.getElementById('reviewCard');

            // 显示加载状态
            btn.disabled = true;
            loading.style.display = 'block';
            reviewCard.style.display = 'none';

            try {
                const response = await fetch('/api/review_plan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displayReviewResult(data);
                    reviewCard.style.display = 'block';
                } else {
                    alert('审查失败: ' + data.error);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 显示审查结果
        function displayReviewResult(data) {
            const content = document.getElementById('reviewResult');

            content.innerHTML = `
                <div class="mb-3">
                    <h6>📊 总体评分</h6>
                    <div class="text-center">
                        <div class="display-6 fw-bold text-primary">${data.score}</div>
                        <div class="badge bg-primary fs-6">${data.grade}</div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>✅ 检查项目</h6>
                    <ul class="list-unstyled ms-2">
                        ${data.checks.map(check => `
                            <li class="mb-1">
                                <i class="fas ${check.passed ? 'fa-check text-success' : 'fa-times text-danger'} me-2"></i>
                                ${check.item}
                            </li>
                        `).join('')}
                    </ul>
                </div>

                ${data.suggestions.length > 0 ? `
                <div class="mb-3">
                    <h6>💡 改进建议</h6>
                    <ul class="list-unstyled ms-2">
                        ${data.suggestions.map(suggestion => `
                            <li class="mb-1">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                ${suggestion}
                            </li>
                        `).join('')}
                    </ul>
                </div>
                ` : ''}
            `;
        }

        // 复制方案
        function copyPlan() {
            const content = document.getElementById('planContent').textContent;

            navigator.clipboard.writeText(content).then(function() {
                const btn = document.getElementById('copyPlan');
                const originalText = btn.innerHTML;

                btn.innerHTML = '✅ 已复制';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-success');

                setTimeout(function() {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-primary');
                }, 2000);
            }).catch(function(err) {
                alert('复制失败: ' + err);
            });
        }

        function getRiskColor(riskLevel) {
            switch (riskLevel) {
                case '低风险':
                    return 'bg-success';
                case '中等风险':
                    return 'bg-warning';
                case '高风险':
                    return 'bg-danger';
                case '重大风险':
                    return 'bg-dark';
                default:
                    return 'bg-secondary';
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/analyze', methods=['POST'])
def analyze():
    """简化的分析API"""
    try:
        data = request.get_json()
        description = data.get('description', '')

        # 简化的参数提取
        result = extract_simple_parameters(description)

        return jsonify({
            'success': True,
            **result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/generate_plan', methods=['POST'])
def generate_plan():
    """生成施工方案API"""
    try:
        data = request.get_json()
        description = data.get('description', '')

        # 提取参数
        params = extract_simple_parameters(description)

        # 生成完整方案
        plan = generate_construction_plan(params, description)

        return jsonify({
            'success': True,
            'plan': plan
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/review_plan', methods=['POST'])
def review_plan():
    """审查施工方案API"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        # 审查方案
        result = review_construction_plan(content)

        return jsonify({
            'success': True,
            **result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def extract_simple_parameters(description):
    """简化的参数提取函数"""
    # 提取跨径
    span_pattern = r'(\d+(?:\.\d+)?)\s*[+×x]\s*(\d+(?:\.\d+)?)'
    spans = re.findall(span_pattern, description)
    max_span = 0
    if spans:
        for span_group in spans:
            for span in span_group:
                max_span = max(max_span, float(span))
    
    # 提取梁高
    height_pattern = r'梁高[：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
    height_match = re.search(height_pattern, description)
    beam_height = float(height_match.group(1)) if height_match else 2.0
    
    # 提取桥宽
    width_pattern = r'[桥宽|宽度][：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
    width_match = re.search(width_pattern, description)
    bridge_width = float(width_match.group(1)) if width_match else 20.0
    
    # 判断工程类型
    project_type = "现浇箱梁"
    if '箱梁' in description:
        project_type = "现浇箱梁"
    elif '板梁' in description or '实体板' in description:
        project_type = "现浇板梁"
    
    # 环境因素
    environmental_factors = []
    if '跨河' in description or '跨渠' in description:
        environmental_factors.append('水体跨越')
    if '跨路' in description:
        environmental_factors.append('道路跨越')
    if '管线' in description:
        environmental_factors.append('管线保护')
    
    # 风险评估
    risk_score = 0
    if max_span > 40:
        risk_score += 2
    elif max_span > 30:
        risk_score += 1
    
    if beam_height > 2.5:
        risk_score += 1
    
    risk_score += len(environmental_factors)
    
    if risk_score >= 3:
        risk_level = "高风险"
    elif risk_score >= 2:
        risk_level = "中等风险"
    else:
        risk_level = "低风险"
    
    # 推荐方案
    if max_span > 30:
        support_system = "碗扣式满堂红脚手架"
        formwork_system = "清水模板+槽钢龙骨"
    else:
        support_system = "扣件式脚手架"
        formwork_system = "清水模板+方木龙骨"
    
    foundation = "15cm二灰碎石垫层，压实度≥95%"
    
    special_measures = []
    if '水体跨越' in environmental_factors:
        special_measures.append("采用贝雷片跨越水体")
    if '道路跨越' in environmental_factors:
        special_measures.append("设置门洞保证交通")
    if '管线保护' in environmental_factors:
        special_measures.append("管线保护专项措施")
    
    return {
        'project_type': project_type,
        'span_length': max_span,
        'beam_height': beam_height,
        'bridge_width': bridge_width,
        'environmental_factors': environmental_factors,
        'risk_level': risk_level,
        'support_system': support_system,
        'formwork_system': formwork_system,
        'foundation': foundation,
        'special_measures': special_measures
    }

def generate_construction_plan(params, description):
    """生成完整的施工方案"""

    # 提取项目名称
    project_name = "专项施工方案"
    if "京台" in description:
        project_name = "京台高速公路箱梁模架专项施工方案"
    elif "箱梁" in description:
        project_name = "现浇箱梁模架专项施工方案"
    elif "板梁" in description:
        project_name = "现浇板梁模架专项施工方案"

    plan = f"""# {project_name}

## 一、编制依据

### 1.1 国家、行业和地方相关规范规程
- GB50204-2015 混凝土结构工程施工质量验收规范
- JGJ162-2008 建筑施工模板安全技术规范
- JGJ166-2008 建筑施工碗扣式钢管脚手架安全技术规范
- JGJ130-2011 建筑施工扣件式钢管脚手架安全技术规范
- GB50666-2011 混凝土结构工程施工规范

### 1.2 安全管理法规文件
- 中华人民共和国安全生产法
- 建设工程安全生产管理条例
- 危险性较大的分部分项工程安全管理办法（建质[2009]87号）

### 1.3 设计文件
- 施工图纸
- 地质勘查报告

## 二、工程概况

### 2.1 结构概况
- 工程类型：{params['project_type']}
- 跨径：{params['span_length']}m
- 梁高：{params['beam_height']}m
- 桥宽：{params['bridge_width']}m

### 2.2 现场条件
- 环境因素：{', '.join(params['environmental_factors']) if params['environmental_factors'] else '无特殊环境因素'}
- 风险等级：{params['risk_level']}

## 三、模架体系选择

### 3.1 选择原则
- 安全可靠，施工操作方便，经济合理
- 满足结构受力要求和变形控制要求
- 适应现场环境条件

### 3.2 支撑体系
- 类型：{params['support_system']}
- 立杆间距：腹板处60cm，其他部位90cm
- 水平杆步距：120cm，顶部60cm
- 剪刀撑：纵横向每4.5m设置，斜杆与地面夹角45°~60°

### 3.3 模板系统
- 底模：{params['formwork_system']}
- 侧模：1.5cm清水模板
- 主龙骨：10#槽钢，横桥向布置
- 次龙骨：10×10cm方木，顺桥向布置，间距20cm

## 四、模架设计

### 4.1 基础处理
- 地基处理：{params['foundation']}
- 排水措施：设置0.5%横坡，四周设排水沟
- 承载力要求：地基承载力不小于160kPa

### 4.2 排架搭设
- 底托下铺设20cm宽、5cm厚大板
- 底部距地面30cm处设置纵、横向扫地杆
- 顶部自由端长度不大于70cm
- 按设计预拱度值设置预拱度

### 4.3 特殊部位处理"""

    # 添加特殊措施
    if params['special_measures']:
        plan += "\n\n### 4.4 特殊措施\n"
        for i, measure in enumerate(params['special_measures'], 1):
            plan += f"- {measure}\n"

    plan += f"""

## 五、施工工艺

### 5.1 技术准备
- 熟悉施工图纸，分析结构特点
- 编制专项施工方案和技术交底
- 施工人员进行三级安全教育和考核

### 5.2 材料准备
- 碗扣架：φ48×3.5mm，满足质量要求
- 清水模板：1.5cm厚，平整无翘曲
- 方木：10×10cm，含水率≤18%
- 槽钢：10#槽钢，符合材质要求

### 5.3 施工流程
1. 基础处理 → 2. 搭设排架 → 3. 排架验收 → 4. 铺设底模
5. 绑扎钢筋 → 6. 安装侧模 → 7. 浇筑混凝土 → 8. 养生
9. 预应力张拉 → 10. 拆除排架

### 5.4 技术要求
- 排架垂直度：≤L/500且不大于50mm
- 底模高程误差：±10mm
- 模板平整度：5mm
- 模板拼缝：≤2mm

## 六、安全保证措施

### 6.1 高空作业安全
- 作业人员必须系安全带，安全带系在牢固处
- 设置防护栏杆，高度120cm，设上下两道横杆
- 满挂密目安全网，网目密度符合要求
- 搭设安全通道和作业平台

### 6.2 排架搭设安全
- 架子工持证上岗，进行安全技术培训
- 严格按方案搭设，不得随意改变
- 连接节点牢固，扣件拧紧力矩40-65N·m
- 及时设置剪刀撑和连墙件

### 6.3 机械设备安全
- 设备进场前检查验收，性能良好
- 操作人员持证上岗，熟悉操作规程
- 定期维护保养，建立设备档案
- 5级以上大风停止吊装作业

### 6.4 用电安全
- 执行"一机一闸一漏一箱"制度
- 设置漏电保护装置，定期检测
- 电气线路架空敷设，避免拖地
- 专业电工负责临电管理

### 6.5 防火安全
- 配备足够的消防器材
- 建立动火作业审批制度
- 易燃物品分类存放，远离火源
- 定期进行消防安全检查

## 七、应急预案

### 7.1 组织机构
- 应急指挥小组：项目经理任组长
- 24小时值班制度，确保通讯畅通
- 明确各级人员职责和联系方式

### 7.2 重点防范
- 高处坠落：加强防护措施，规范作业行为
- 支架坍塌：严格按方案施工，加强检查验收
- 触电事故：规范用电管理，定期检查线路
- 物体打击：佩戴安全帽，设置防护设施

### 7.3 应急措施
- 发生事故立即报告，启动应急预案
- 迅速组织救援，保护事故现场
- 及时送医救治，做好善后工作
- 分析事故原因，制定防范措施

## 八、计算验证

### 8.1 荷载计算
- 恒载：模板自重0.5kPa + 混凝土自重{params['beam_height']*25}kPa
- 活载：施工荷载2.5kPa + 振捣荷载2.0kPa
- 荷载组合：强度验算1.2恒载+1.4活载

### 8.2 结构验算
- 模板强度、刚度验算
- 支撑体系承载力、稳定性验算
- 地基承载力验算
- 预拱度设置计算

### 8.3 验算结论
根据计算结果，所选模架体系满足：
- 强度要求：应力≤容许应力
- 刚度要求：挠度≤L/400
- 稳定性要求：稳定系数≥2.0
- 地基承载力：满足承载要求

## 九、质量控制

### 9.1 材料质量控制
- 进场材料检查验收，索要合格证
- 不合格材料不得使用
- 建立材料使用台账

### 9.2 施工过程控制
- 严格按方案施工，不得随意变更
- 关键工序设置质量控制点
- 隐蔽工程及时验收，做好记录

### 9.3 成品保护
- 制定成品保护措施
- 明确保护责任人
- 定期检查保护效果

## 十、环保措施

### 10.1 噪音控制
- 合理安排施工时间，避免夜间施工
- 选用低噪音设备，定期维护
- 设置隔音屏障，减少噪音影响

### 10.2 扬尘控制
- 施工现场洒水降尘
- 材料堆放覆盖防尘网
- 运输车辆冲洗干净

### 10.3 废料处理
- 分类收集建筑垃圾
- 可回收材料重复利用
- 废料运至指定地点处置

---

本方案编制依据充分，技术措施可行，安全措施完善，
请各参建单位严格按照本方案组织施工。

编制单位：施工单位
编制时间：{__import__('datetime').datetime.now().strftime('%Y年%m月%d日')}
"""

    return plan

def review_construction_plan(content):
    """审查施工方案"""

    # 检查项目列表
    check_items = [
        {'item': '编制依据完整', 'keywords': ['GB', 'JGJ', '规范', '标准']},
        {'item': '工程概况详细', 'keywords': ['工程', '概况', '跨径', '梁高']},
        {'item': '技术方案合理', 'keywords': ['支撑', '模板', '方案', '选择']},
        {'item': '安全措施完善', 'keywords': ['安全', '防护', '措施', '应急']},
        {'item': '计算验证充分', 'keywords': ['计算', '验算', '荷载', '强度']},
        {'item': '质量控制明确', 'keywords': ['质量', '控制', '检查', '验收']},
        {'item': '施工工艺清晰', 'keywords': ['施工', '工艺', '流程', '步骤']},
        {'item': '应急预案完备', 'keywords': ['应急', '预案', '事故', '救援']}
    ]

    # 执行检查
    checks = []
    passed_count = 0

    for check in check_items:
        # 检查是否包含关键词
        passed = any(keyword in content for keyword in check['keywords'])
        checks.append({
            'item': check['item'],
            'passed': passed
        })
        if passed:
            passed_count += 1

    # 计算得分
    score = round((passed_count / len(check_items)) * 100, 1)

    # 确定等级
    if score >= 90:
        grade = "优秀"
    elif score >= 80:
        grade = "良好"
    elif score >= 70:
        grade = "合格"
    else:
        grade = "不合格"

    # 生成改进建议
    suggestions = []

    if score < 100:
        failed_items = [check['item'] for check in checks if not check['passed']]

        if '编制依据完整' in failed_items:
            suggestions.append("补充相关技术标准和规范依据")

        if '工程概况详细' in failed_items:
            suggestions.append("完善工程基本信息和现场条件描述")

        if '技术方案合理' in failed_items:
            suggestions.append("详细说明技术方案选择理由和具体措施")

        if '安全措施完善' in failed_items:
            suggestions.append("加强安全防护措施和风险控制")

        if '计算验证充分' in failed_items:
            suggestions.append("补充结构计算和验证过程")

        if '质量控制明确' in failed_items:
            suggestions.append("明确质量控制标准和检查要求")

        if '施工工艺清晰' in failed_items:
            suggestions.append("细化施工工艺流程和技术要求")

        if '应急预案完备' in failed_items:
            suggestions.append("完善应急预案和救援措施")

    # 通用建议
    if score < 90:
        suggestions.append("建议对照相关规范标准进一步完善方案")

    if '高大模板' in content or '危险性较大' in content:
        suggestions.append("该工程属于危大工程，建议组织专家论证")

    return {
        'score': score,
        'grade': grade,
        'checks': checks,
        'suggestions': suggestions
    }

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
