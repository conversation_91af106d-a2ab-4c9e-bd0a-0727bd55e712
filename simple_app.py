#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版施工方案智能体Web应用
"""

from flask import Flask, render_template_string, request, jsonify
import re

app = Flask(__name__)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工方案编制与审查智能体</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .result-card {
            margin-top: 2rem;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                🏗️ 施工方案智能体
            </a>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">施工方案编制与审查智能体</h1>
            <p class="lead mb-4">基于京台4标箱梁模架专项施工方案学习开发的AI助手</p>
            <p class="mb-0">自动分析工程参数 • 智能生成技术方案 • 全面审查安全措施</p>
        </div>
    </section>

    <!-- 主要功能区域 -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">🔍 工程参数分析</h5>
                        </div>
                        <div class="card-body">
                            <textarea id="projectDescription" class="form-control" rows="6" 
                                placeholder="请输入工程描述，例如：&#10;京台高速公路现浇箱梁工程，跨径35+38+35m，梁高2.0m，桥宽42m，上跨现况道路，需设置门洞保证交通。地质条件为粉砂土层。"></textarea>
                            <button id="analyzeBtn" class="btn btn-primary mt-3" onclick="analyzeProject()">
                                开始分析
                            </button>
                            <div id="loading" style="display: none;" class="mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">分析中...</span>
                                </div>
                                <span class="ms-2">正在分析工程参数...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div id="resultCard" class="card result-card" style="display: none;">
                        <div class="card-header">
                            <h5 class="mb-0">📊 分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2024 施工方案编制与审查智能体 - 基于AI技术的建筑施工辅助系统</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function analyzeProject() {
            const description = document.getElementById('projectDescription').value.trim();
            
            if (!description) {
                alert('请输入工程描述');
                return;
            }
            
            const btn = document.getElementById('analyzeBtn');
            const loading = document.getElementById('loading');
            const resultCard = document.getElementById('resultCard');
            
            // 显示加载状态
            btn.disabled = true;
            loading.style.display = 'block';
            resultCard.style.display = 'none';
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        description: description
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResult(data);
                    resultCard.style.display = 'block';
                } else {
                    alert('分析失败: ' + data.error);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        function displayResult(data) {
            const content = document.getElementById('resultContent');
            
            content.innerHTML = `
                <div class="mb-3">
                    <h6>🏗️ 工程参数</h6>
                    <ul class="list-unstyled ms-3">
                        <li><strong>工程类型:</strong> ${data.project_type}</li>
                        <li><strong>最大跨径:</strong> ${data.span_length}m</li>
                        <li><strong>梁高:</strong> ${data.beam_height}m</li>
                        <li><strong>桥宽:</strong> ${data.bridge_width}m</li>
                        <li><strong>环境因素:</strong> ${data.environmental_factors.join(', ') || '无特殊因素'}</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>⚙️ 推荐方案</h6>
                    <div class="ms-3">
                        <p><strong>支撑体系:</strong> ${data.support_system}</p>
                        <p><strong>模板系统:</strong> ${data.formwork_system}</p>
                        <p><strong>基础处理:</strong> ${data.foundation}</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>⚠️ 风险评估</h6>
                    <span class="badge ${getRiskColor(data.risk_level)} fs-6">${data.risk_level}</span>
                </div>
                
                ${data.special_measures.length > 0 ? `
                <div class="mb-3">
                    <h6>🔧 特殊措施</h6>
                    <ul class="ms-3">
                        ${data.special_measures.map(measure => `<li>${measure}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
            `;
        }

        function getRiskColor(riskLevel) {
            switch (riskLevel) {
                case '低风险':
                    return 'bg-success';
                case '中等风险':
                    return 'bg-warning';
                case '高风险':
                    return 'bg-danger';
                case '重大风险':
                    return 'bg-dark';
                default:
                    return 'bg-secondary';
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/analyze', methods=['POST'])
def analyze():
    """简化的分析API"""
    try:
        data = request.get_json()
        description = data.get('description', '')
        
        # 简化的参数提取
        result = extract_simple_parameters(description)
        
        return jsonify({
            'success': True,
            **result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def extract_simple_parameters(description):
    """简化的参数提取函数"""
    # 提取跨径
    span_pattern = r'(\d+(?:\.\d+)?)\s*[+×x]\s*(\d+(?:\.\d+)?)'
    spans = re.findall(span_pattern, description)
    max_span = 0
    if spans:
        for span_group in spans:
            for span in span_group:
                max_span = max(max_span, float(span))
    
    # 提取梁高
    height_pattern = r'梁高[：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
    height_match = re.search(height_pattern, description)
    beam_height = float(height_match.group(1)) if height_match else 2.0
    
    # 提取桥宽
    width_pattern = r'[桥宽|宽度][：:]\s*(\d+(?:\.\d+)?)\s*[m米]'
    width_match = re.search(width_pattern, description)
    bridge_width = float(width_match.group(1)) if width_match else 20.0
    
    # 判断工程类型
    project_type = "现浇箱梁"
    if '箱梁' in description:
        project_type = "现浇箱梁"
    elif '板梁' in description or '实体板' in description:
        project_type = "现浇板梁"
    
    # 环境因素
    environmental_factors = []
    if '跨河' in description or '跨渠' in description:
        environmental_factors.append('水体跨越')
    if '跨路' in description:
        environmental_factors.append('道路跨越')
    if '管线' in description:
        environmental_factors.append('管线保护')
    
    # 风险评估
    risk_score = 0
    if max_span > 40:
        risk_score += 2
    elif max_span > 30:
        risk_score += 1
    
    if beam_height > 2.5:
        risk_score += 1
    
    risk_score += len(environmental_factors)
    
    if risk_score >= 3:
        risk_level = "高风险"
    elif risk_score >= 2:
        risk_level = "中等风险"
    else:
        risk_level = "低风险"
    
    # 推荐方案
    if max_span > 30:
        support_system = "碗扣式满堂红脚手架"
        formwork_system = "清水模板+槽钢龙骨"
    else:
        support_system = "扣件式脚手架"
        formwork_system = "清水模板+方木龙骨"
    
    foundation = "15cm二灰碎石垫层，压实度≥95%"
    
    special_measures = []
    if '水体跨越' in environmental_factors:
        special_measures.append("采用贝雷片跨越水体")
    if '道路跨越' in environmental_factors:
        special_measures.append("设置门洞保证交通")
    if '管线保护' in environmental_factors:
        special_measures.append("管线保护专项措施")
    
    return {
        'project_type': project_type,
        'span_length': max_span,
        'beam_height': beam_height,
        'bridge_width': bridge_width,
        'environmental_factors': environmental_factors,
        'risk_level': risk_level,
        'support_system': support_system,
        'formwork_system': formwork_system,
        'foundation': foundation,
        'special_measures': special_measures
    }

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
